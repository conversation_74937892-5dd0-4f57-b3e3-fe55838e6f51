import os
import socket
import threading
import time
import subprocess
import sys
from datetime import datetime
from app import create_app, db
from app.utils.system_info import get_machine_id

app = create_app()

def get_ip_address():
    """Get the server's IP address"""
    try:
        # Get hostname
        hostname = socket.gethostname()
        # Get IP address
        ip_address = socket.gethostbyname(hostname)
        return ip_address
    except:
        return "127.0.0.1"  # Default to localhost if unable to determine

def start_admin_tool():
    """Start the standalone admin tool in a separate thread"""
    try:
        print("🔧 Starting Standalone Admin Tool...")

        # Import the admin tool Flask app
        import importlib.util
        spec = importlib.util.spec_from_file_location("standalone_admin_tool", "standalone_admin_tool.py")
        admin_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(admin_module)

        # Get the Flask app from the admin tool
        admin_app = admin_module.app

        # Start the admin tool in a separate thread
        def run_admin_tool():
            try:
                admin_app.run(host='127.0.0.1', port=8081, debug=False, use_reloader=False)
            except Exception as e:
                print(f"❌ Admin tool error: {e}")

        admin_thread = threading.Thread(target=run_admin_tool, daemon=True)
        admin_thread.start()

        # Wait a moment to check if it started successfully
        time.sleep(2)

        # Test if the admin tool is responding
        try:
            import urllib.request
            urllib.request.urlopen('http://127.0.0.1:8081', timeout=1)
            print("✅ Standalone Admin Tool started successfully on port 8081")
            return admin_thread
        except:
            # If direct test fails, assume it's starting up
            print("✅ Standalone Admin Tool starting on port 8081")
            return admin_thread

    except Exception as e:
        print(f"❌ Error starting Standalone Admin Tool: {e}")
        return None

def monitor_admin_tool(admin_thread):
    """Monitor standalone admin tool thread in background"""
    if admin_thread:
        def monitor():
            admin_thread.join()
            print("⚠️ Standalone Admin Tool thread ended")

        monitor_thread = threading.Thread(target=monitor, daemon=True)
        monitor_thread.start()

if __name__ == "__main__":
    host = os.getenv('HOST', '0.0.0.0')
    port = int(os.getenv('PORT', 5000))
    debug = os.getenv('FLASK_DEBUG', 'True').lower() in ('true', 't', '1')

    # Print server information
    print("\n" + "="*70)
    print(" 🏢 UNIFIED DEVICE REPAIR MANAGEMENT SYSTEM")
    print("="*70)
    print(f" 🚀 Server Status: ONLINE")
    print(f" ⏰ Server Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print(f" 🖥️  Machine ID: {get_machine_id()}")
    print(f" 🌐 Main Application: http://127.0.0.1:{port}")
    print(f" 🔧 Standalone Admin Tool: http://127.0.0.1:8081")
    print(f" 🔗 Network URL: http://{get_ip_address()}:{port}")
    print(f" 🐛 Debug Mode: {'Enabled' if debug else 'Disabled'}")
    print("="*70)
    print(" 🎯 FEATURES:")
    print("   ✅ Device Repair Management")
    print("   ✅ Enhanced Inventory Management")
    print("   ✅ Enhanced Accounting System")
    print("   🔓 Open Access Mode (Activation Bypassed)")
    print("   ✅ MariaDB Support with SQLite Fallback")
    print("   ✅ Standalone Admin Tool Integration")
    print("   ✅ Real-time Status Monitoring")
    print("="*70 + "\n")

    # Start standalone admin tool in background
    admin_thread = start_admin_tool()
    if admin_thread:
        monitor_admin_tool(admin_thread)
        print("🔗 Both applications are now running in unified mode")
        print("📋 Access main app for device management and activation")
        print("🔧 Standalone admin tool runs automatically in background")
        print("🎯 Generate activation codes at: http://127.0.0.1:8081")
        print("-"*70 + "\n")
    else:
        print("⚠️ Standalone admin tool failed to start, continuing with main app only")
        print("   You can start it manually: python standalone_admin_tool.py")
        print("-"*70 + "\n")

    try:
        app.run(host=host, port=port, debug=debug, use_reloader=False)
    except KeyboardInterrupt:
        print("\n🛑 Shutting down unified system...")
        print("✅ Main application stopped")
        print("✅ Standalone admin tool will stop automatically (daemon thread)")
        print("👋 Goodbye!")
    except Exception as e:
        print(f"\n❌ Error running main application: {e}")
        print("✅ Standalone admin tool will stop automatically (daemon thread)")
        print("❌ System shutdown due to error")