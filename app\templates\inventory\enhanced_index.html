{% extends 'base.html' %}

{% block title %}Enhanced Inventory Management{% endblock %}

{% block extra_css %}
<style>
.inventory-card {
    transition: transform 0.2s, box-shadow 0.2s;
    border: none;
    border-radius: 12px;
    overflow: hidden;
}

.inventory-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.stock-badge {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 10;
}

.search-container {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
}

.filter-section {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1.5rem;
    margin-bottom: 2rem;
}

.stats-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    margin-bottom: 1rem;
}

.part-image {
    width: 100%;
    height: 200px;
    object-fit: cover;
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 3rem;
    color: #6c757d;
}

.price-tag {
    font-size: 1.25rem;
    font-weight: bold;
    color: #28a745;
}

.quantity-display {
    font-size: 1.1rem;
    font-weight: 600;
}

.btn-action {
    border-radius: 20px;
    padding: 0.375rem 1rem;
    font-size: 0.875rem;
}

.search-suggestions {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 0 0 8px 8px;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    display: none;
}

.search-suggestion {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.2s;
}

.search-suggestion:hover {
    background-color: #f8f9fa;
}

.search-suggestion:last-child {
    border-bottom: none;
}
</style>
{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Header -->
    <div class="d-flex justify-content-between align-items-center mb-4">
        <h1><i class="fas fa-boxes me-2"></i>Enhanced Inventory Management</h1>
        <div>
            <a href="{{ url_for('inventory_enhanced.add_part') }}" class="btn btn-primary">
                <i class="fas fa-plus me-2"></i>Add New Part
            </a>
            <a href="{{ url_for('inventory_enhanced.reports') }}" class="btn btn-info">
                <i class="fas fa-chart-bar me-2"></i>Reports
            </a>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-3">
            <div class="stats-card">
                <i class="fas fa-boxes fa-2x mb-2"></i>
                <h3>{{ total_parts }}</h3>
                <p class="mb-0">Total Parts</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <i class="fas fa-dollar-sign fa-2x mb-2"></i>
                <h3>${{ "%.2f"|format(total_value) }}</h3>
                <p class="mb-0">Total Value</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <h3>{{ low_stock_count }}</h3>
                <p class="mb-0">Low Stock</p>
            </div>
        </div>
        <div class="col-md-3">
            <div class="stats-card">
                <i class="fas fa-times-circle fa-2x mb-2"></i>
                <h3>{{ out_of_stock_count }}</h3>
                <p class="mb-0">Out of Stock</p>
            </div>
        </div>
    </div>

    <!-- Search and Filters -->
    <div class="search-container">
        <form method="GET" id="searchForm">
            <div class="row">
                <div class="col-md-4">
                    <div class="position-relative">
                        <input type="text" 
                               class="form-control form-control-lg" 
                               name="search" 
                               value="{{ search }}" 
                               placeholder="Search parts, brands, categories..."
                               id="searchInput">
                        <div class="search-suggestions" id="searchSuggestions"></div>
                    </div>
                </div>
                <div class="col-md-2">
                    <select class="form-select form-select-lg" name="category">
                        <option value="">All Categories</option>
                        {% for cat in categories %}
                        <option value="{{ cat }}" {% if cat == category %}selected{% endif %}>{{ cat }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select form-select-lg" name="brand">
                        <option value="">All Brands</option>
                        {% for brand_item in brands %}
                        <option value="{{ brand_item }}" {% if brand_item == brand %}selected{% endif %}>{{ brand_item }}</option>
                        {% endfor %}
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select form-select-lg" name="stock_status">
                        <option value="">All Stock</option>
                        <option value="in_stock" {% if stock_status == 'in_stock' %}selected{% endif %}>In Stock</option>
                        <option value="low_stock" {% if stock_status == 'low_stock' %}selected{% endif %}>Low Stock</option>
                        <option value="out_of_stock" {% if stock_status == 'out_of_stock' %}selected{% endif %}>Out of Stock</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <button type="submit" class="btn btn-light btn-lg w-100">
                        <i class="fas fa-search me-2"></i>Search
                    </button>
                </div>
            </div>
            
            <!-- Advanced Filters -->
            <div class="row mt-3">
                <div class="col-md-2">
                    <input type="number" 
                           class="form-control" 
                           name="min_price" 
                           value="{{ min_price if min_price }}" 
                           placeholder="Min Price"
                           step="0.01">
                </div>
                <div class="col-md-2">
                    <input type="number" 
                           class="form-control" 
                           name="max_price" 
                           value="{{ max_price if max_price }}" 
                           placeholder="Max Price"
                           step="0.01">
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="sort_by">
                        <option value="name" {% if sort_by == 'name' %}selected{% endif %}>Name</option>
                        <option value="price" {% if sort_by == 'price' %}selected{% endif %}>Price</option>
                        <option value="quantity" {% if sort_by == 'quantity' %}selected{% endif %}>Quantity</option>
                        <option value="category" {% if sort_by == 'category' %}selected{% endif %}>Category</option>
                        <option value="brand" {% if sort_by == 'brand' %}selected{% endif %}>Brand</option>
                        <option value="created_at" {% if sort_by == 'created_at' %}selected{% endif %}>Date Added</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="sort_order">
                        <option value="asc" {% if sort_order == 'asc' %}selected{% endif %}>Ascending</option>
                        <option value="desc" {% if sort_order == 'desc' %}selected{% endif %}>Descending</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <select class="form-select" name="per_page">
                        <option value="20" {% if per_page == 20 %}selected{% endif %}>20 per page</option>
                        <option value="50" {% if per_page == 50 %}selected{% endif %}>50 per page</option>
                        <option value="100" {% if per_page == 100 %}selected{% endif %}>100 per page</option>
                    </select>
                </div>
                <div class="col-md-2">
                    <a href="{{ url_for('inventory_enhanced.index') }}" class="btn btn-outline-light w-100">
                        <i class="fas fa-times me-2"></i>Clear
                    </a>
                </div>
            </div>
        </form>
    </div>

    <!-- Results Info -->
    <div class="d-flex justify-content-between align-items-center mb-3">
        <div>
            <span class="text-muted">
                Showing {{ pagination.per_page * (pagination.page - 1) + 1 }} - 
                {{ pagination.per_page * (pagination.page - 1) + spare_parts|length }} 
                of {{ pagination.total }} parts
            </span>
        </div>
        <div class="btn-group" role="group">
            <button type="button" class="btn btn-outline-secondary" id="cardView">
                <i class="fas fa-th-large"></i>
            </button>
            <button type="button" class="btn btn-outline-secondary" id="listView">
                <i class="fas fa-list"></i>
            </button>
        </div>
    </div>

    <!-- Inventory Items -->
    <div id="inventoryContainer">
        <!-- Card View -->
        <div id="cardViewContainer" class="row">
            {% for part in spare_parts %}
            <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                <div class="card inventory-card h-100">
                    <!-- Stock Status Badge -->
                    {% if part.quantity == 0 %}
                        <span class="badge bg-danger stock-badge">Out of Stock</span>
                    {% elif part.quantity <= part.min_quantity %}
                        <span class="badge bg-warning stock-badge">Low Stock</span>
                    {% else %}
                        <span class="badge bg-success stock-badge">In Stock</span>
                    {% endif %}
                    
                    <!-- Part Image Placeholder -->
                    <div class="part-image">
                        <i class="fas fa-cog"></i>
                    </div>
                    
                    <div class="card-body">
                        <h6 class="card-title">{{ part.name }}</h6>
                        <p class="text-muted small mb-2">{{ part.part_number }}</p>
                        
                        <div class="mb-2">
                            <span class="badge bg-primary">{{ part.category or 'Uncategorized' }}</span>
                            {% if part.brand %}
                            <span class="badge bg-secondary">{{ part.brand }}</span>
                            {% endif %}
                        </div>
                        
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="price-tag">${{ "%.2f"|format(part.price) }}</span>
                            <span class="quantity-display">Qty: {{ part.quantity }}</span>
                        </div>
                        
                        {% if part.description %}
                        <p class="card-text small text-muted">{{ part.description[:50] }}{% if part.description|length > 50 %}...{% endif %}</p>
                        {% endif %}
                    </div>
                    
                    <div class="card-footer bg-transparent">
                        <div class="d-flex gap-2">
                            <a href="{{ url_for('inventory_enhanced.part_detail', part_id=part.id) }}" 
                               class="btn btn-primary btn-action flex-fill">
                                <i class="fas fa-eye"></i> View
                            </a>
                            <a href="{{ url_for('inventory_enhanced.edit_part', part_id=part.id) }}" 
                               class="btn btn-outline-secondary btn-action">
                                <i class="fas fa-edit"></i>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        
        <!-- List View -->
        <div id="listViewContainer" class="d-none">
            <div class="table-responsive">
                <table class="table table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Part</th>
                            <th>Category</th>
                            <th>Brand</th>
                            <th>Price</th>
                            <th>Quantity</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        {% for part in spare_parts %}
                        <tr>
                            <td>
                                <div>
                                    <strong>{{ part.name }}</strong><br>
                                    <small class="text-muted">{{ part.part_number }}</small>
                                </div>
                            </td>
                            <td>{{ part.category or '-' }}</td>
                            <td>{{ part.brand or '-' }}</td>
                            <td class="price-tag">${{ "%.2f"|format(part.price) }}</td>
                            <td class="quantity-display">{{ part.quantity }}</td>
                            <td>
                                {% if part.quantity == 0 %}
                                    <span class="badge bg-danger">Out of Stock</span>
                                {% elif part.quantity <= part.min_quantity %}
                                    <span class="badge bg-warning">Low Stock</span>
                                {% else %}
                                    <span class="badge bg-success">In Stock</span>
                                {% endif %}
                            </td>
                            <td>
                                <div class="btn-group">
                                    <a href="{{ url_for('inventory_enhanced.part_detail', part_id=part.id) }}" 
                                       class="btn btn-sm btn-primary">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                    <a href="{{ url_for('inventory_enhanced.edit_part', part_id=part.id) }}" 
                                       class="btn btn-sm btn-outline-secondary">
                                        <i class="fas fa-edit"></i>
                                    </a>
                                </div>
                            </td>
                        </tr>
                        {% endfor %}
                    </tbody>
                </table>
            </div>
        </div>
    </div>

    <!-- Pagination -->
    {% if pagination.pages > 1 %}
    <nav aria-label="Inventory pagination">
        <ul class="pagination justify-content-center">
            {% if pagination.has_prev %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('inventory_enhanced.index', page=pagination.prev_num, **request.args) }}">Previous</a>
            </li>
            {% endif %}
            
            {% for page_num in pagination.iter_pages() %}
                {% if page_num %}
                    {% if page_num != pagination.page %}
                    <li class="page-item">
                        <a class="page-link" href="{{ url_for('inventory_enhanced.index', page=page_num, **request.args) }}">{{ page_num }}</a>
                    </li>
                    {% else %}
                    <li class="page-item active">
                        <span class="page-link">{{ page_num }}</span>
                    </li>
                    {% endif %}
                {% else %}
                <li class="page-item disabled">
                    <span class="page-link">...</span>
                </li>
                {% endif %}
            {% endfor %}
            
            {% if pagination.has_next %}
            <li class="page-item">
                <a class="page-link" href="{{ url_for('inventory_enhanced.index', page=pagination.next_num, **request.args) }}">Next</a>
            </li>
            {% endif %}
        </ul>
    </nav>
    {% endif %}
</div>
{% endblock %}

{% block extra_js %}
<script>
$(document).ready(function() {
    // View toggle functionality
    $('#cardView').click(function() {
        $('#cardViewContainer').removeClass('d-none');
        $('#listViewContainer').addClass('d-none');
        $(this).addClass('active');
        $('#listView').removeClass('active');
        localStorage.setItem('inventoryView', 'card');
    });

    $('#listView').click(function() {
        $('#listViewContainer').removeClass('d-none');
        $('#cardViewContainer').addClass('d-none');
        $(this).addClass('active');
        $('#cardView').removeClass('active');
        localStorage.setItem('inventoryView', 'list');
    });

    // Restore saved view preference
    const savedView = localStorage.getItem('inventoryView');
    if (savedView === 'list') {
        $('#listView').click();
    } else {
        $('#cardView').click();
    }

    // Real-time search functionality
    let searchTimeout;
    $('#searchInput').on('input', function() {
        const query = $(this).val().trim();

        clearTimeout(searchTimeout);

        if (query.length >= 2) {
            searchTimeout = setTimeout(function() {
                performSearch(query);
            }, 300);
        } else {
            $('#searchSuggestions').hide();
        }
    });

    function performSearch(query) {
        $.ajax({
            url: '{{ url_for("inventory_enhanced.api_search") }}',
            data: { q: query, limit: 8 },
            success: function(data) {
                displaySearchSuggestions(data);
            },
            error: function() {
                $('#searchSuggestions').hide();
            }
        });
    }

    function displaySearchSuggestions(suggestions) {
        const container = $('#searchSuggestions');
        container.empty();

        if (suggestions.length === 0) {
            container.hide();
            return;
        }

        suggestions.forEach(function(item) {
            const suggestion = $(`
                <div class="search-suggestion" data-id="${item.id}">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${item.name}</strong><br>
                            <small class="text-muted">${item.part_number} - ${item.brand || 'No Brand'}</small>
                        </div>
                        <div class="text-end">
                            <span class="badge bg-${getStockStatusColor(item.stock_status)}">${getStockStatusText(item.stock_status)}</span><br>
                            <small>$${item.price.toFixed(2)} (${item.quantity})</small>
                        </div>
                    </div>
                </div>
            `);

            suggestion.click(function() {
                window.location.href = `/inventory/part/${item.id}`;
            });

            container.append(suggestion);
        });

        container.show();
    }

    function getStockStatusColor(status) {
        switch(status) {
            case 'in_stock': return 'success';
            case 'low_stock': return 'warning';
            case 'out_of_stock': return 'danger';
            default: return 'secondary';
        }
    }

    function getStockStatusText(status) {
        switch(status) {
            case 'in_stock': return 'In Stock';
            case 'low_stock': return 'Low Stock';
            case 'out_of_stock': return 'Out of Stock';
            default: return 'Unknown';
        }
    }

    // Hide suggestions when clicking outside
    $(document).click(function(e) {
        if (!$(e.target).closest('#searchInput, #searchSuggestions').length) {
            $('#searchSuggestions').hide();
        }
    });

    // Auto-submit form when filters change
    $('select[name="category"], select[name="brand"], select[name="stock_status"], select[name="sort_by"], select[name="sort_order"], select[name="per_page"]').change(function() {
        $('#searchForm').submit();
    });

    // Enhanced card hover effects
    $('.inventory-card').hover(
        function() {
            $(this).find('.card-footer').addClass('bg-light');
        },
        function() {
            $(this).find('.card-footer').removeClass('bg-light');
        }
    );

    // Keyboard shortcuts
    $(document).keydown(function(e) {
        // Ctrl/Cmd + K to focus search
        if ((e.ctrlKey || e.metaKey) && e.keyCode === 75) {
            e.preventDefault();
            $('#searchInput').focus();
        }

        // Escape to clear search
        if (e.keyCode === 27) {
            $('#searchInput').val('').focus();
            $('#searchSuggestions').hide();
        }
    });

    // Add search hint
    $('#searchInput').attr('title', 'Press Ctrl+K to focus, Escape to clear');
});
</script>
{% endblock %}
