from datetime import datetime, timedelta
from flask import Blueprint, render_template, redirect, url_for, flash, request, jsonify
from flask_login import login_required, current_user
from sqlalchemy import func, desc, and_, or_
from app.models import RepairTicket, Device, User
from app.models.spare_part import SparePart, StockMovement, RepairPartUsage
from app.models.spare_parts_enhanced import SaleTransaction, SaleTransactionItem, InventoryValuation
from app.extensions import db

accounting_bp = Blueprint('accounting', __name__, url_prefix='/accounting')

@accounting_bp.route('/')
@login_required
def index():
    # Only admins can access accounting
    if not current_user.is_admin():
        flash('You do not have permission to access this page', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get summary of tickets by payment status
    paid_count = RepairTicket.query.filter_by(payment_status='paid').count()
    pending_count = RepairTicket.query.filter_by(payment_status='pending').count()
    partial_count = RepairTicket.query.filter_by(payment_status='partial').count()

    # Get total revenue
    total_revenue = RepairTicket.query.filter_by(payment_status='paid').with_entities(func.sum(RepairTicket.cost)).scalar() or 0

    # Get monthly revenue
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    monthly_revenue = RepairTicket.query.filter(
        RepairTicket.payment_status == 'paid',
        RepairTicket.updated_at >= thirty_days_ago
    ).with_entities(func.sum(RepairTicket.cost)).scalar() or 0

    # Get recent payments
    recent_payments = RepairTicket.query.filter_by(payment_status='paid').order_by(desc(RepairTicket.updated_at)).limit(10).all()

    # Get pending payments
    pending_payments = RepairTicket.query.filter_by(payment_status='pending').order_by(desc(RepairTicket.updated_at)).all()

    return render_template(
        'accounting/index.html',
        paid_count=paid_count,
        pending_count=pending_count,
        partial_count=partial_count,
        total_revenue=total_revenue,
        monthly_revenue=monthly_revenue,
        recent_payments=recent_payments,
        pending_payments=pending_payments
    )

@accounting_bp.route('/invoices')
@login_required
def invoices():
    # Admins can see all invoices, customers can see only their own
    if current_user.is_admin():
        repair_tickets = RepairTicket.query.order_by(desc(RepairTicket.created_at)).all()
    else:
        repair_tickets = RepairTicket.query.join(Device).filter(
            Device.customer_id == current_user.id
        ).order_by(desc(RepairTicket.created_at)).all()

    return render_template('accounting/invoices.html', repair_tickets=repair_tickets)

@accounting_bp.route('/invoice/<int:ticket_id>')
@login_required
def invoice(ticket_id):
    ticket = RepairTicket.query.get_or_404(ticket_id)
    device = Device.query.get_or_404(ticket.device_id)

    # Check permissions
    if not current_user.is_admin() and device.customer_id != current_user.id:
        flash('You do not have permission to view this invoice', 'danger')
        return redirect(url_for('accounting.invoices'))

    # Get customer
    customer = User.query.get(device.customer_id)

    return render_template('accounting/invoice.html', ticket=ticket, device=device, customer=customer)

@accounting_bp.route('/update_payment/<int:ticket_id>', methods=['GET', 'POST'])
@login_required
def update_payment(ticket_id):
    # Only admins can update payment status
    if not current_user.is_admin():
        flash('You do not have permission to access this page', 'danger')
        return redirect(url_for('dashboard.index'))

    ticket = RepairTicket.query.get_or_404(ticket_id)

    if request.method == 'POST':
        payment_status = request.form.get('payment_status')

        ticket.payment_status = payment_status
        db.session.commit()

        flash('Payment status updated successfully', 'success')
        return redirect(url_for('accounting.invoice', ticket_id=ticket_id))

    return render_template('accounting/update_payment.html', ticket=ticket)

@accounting_bp.route('/reports')
@login_required
def reports():
    # Only admins can access reports
    if not current_user.is_admin():
        flash('You do not have permission to access this page', 'danger')
        return redirect(url_for('dashboard.index'))

    # Get revenue by month for the last 6 months
    revenues = []
    for i in range(6):
        start_date = datetime.utcnow().replace(day=1) - timedelta(days=30 * i)
        end_date = start_date.replace(day=1, month=start_date.month + 1) if start_date.month < 12 else start_date.replace(day=1, month=1, year=start_date.year + 1)

        revenue = RepairTicket.query.filter(
            RepairTicket.payment_status == 'paid',
            RepairTicket.updated_at >= start_date,
            RepairTicket.updated_at < end_date
        ).with_entities(func.sum(RepairTicket.cost)).scalar() or 0

        revenues.append({
            'month': start_date.strftime('%B %Y'),
            'revenue': revenue
        })

    # Reverse to show oldest to newest
    revenues.reverse()

    # Get top 5 customers by revenue
    top_customers = db.session.query(
        User, func.sum(RepairTicket.cost).label('total_spent')
    ).join(Device, Device.customer_id == User.id).join(
        RepairTicket, RepairTicket.device_id == Device.id
    ).filter(
        RepairTicket.payment_status == 'paid'
    ).group_by(User.id).order_by(desc('total_spent')).limit(5).all()

    return render_template('accounting/reports.html', revenues=revenues, top_customers=top_customers)

@accounting_bp.route('/inventory-integration')
@login_required
def inventory_integration():
    """Inventory and accounting integration dashboard"""
    if not current_user.is_admin():
        flash('You do not have permission to access this page', 'danger')
        return redirect(url_for('dashboard.index'))

    # Inventory value analysis
    total_inventory_value = db.session.query(func.sum(SparePart.price * SparePart.quantity)).scalar() or 0
    total_inventory_cost = db.session.query(func.sum(SparePart.cost * SparePart.quantity)).scalar() or 0
    potential_profit = total_inventory_value - total_inventory_cost

    # Parts usage in repairs (revenue from parts)
    parts_revenue = db.session.query(
        func.sum(RepairPartUsage.quantity * SparePart.price)
    ).join(SparePart).scalar() or 0

    # Low stock financial impact
    low_stock_value = db.session.query(
        func.sum(SparePart.price * SparePart.quantity)
    ).filter(and_(SparePart.quantity > 0, SparePart.quantity <= SparePart.min_quantity)).scalar() or 0

    # Category-wise inventory value
    category_analysis = db.session.query(
        SparePart.category,
        func.sum(SparePart.price * SparePart.quantity).label('total_value'),
        func.sum(SparePart.cost * SparePart.quantity).label('total_cost'),
        func.count(SparePart.id).label('part_count'),
        func.sum(SparePart.quantity).label('total_quantity')
    ).group_by(SparePart.category).all()

    # Recent financial transactions involving inventory
    recent_part_usage = db.session.query(RepairPartUsage).join(SparePart).join(RepairTicket).order_by(
        desc(RepairPartUsage.created_at)
    ).limit(10).all()

    return render_template(
        'accounting/inventory_integration.html',
        total_inventory_value=total_inventory_value,
        total_inventory_cost=total_inventory_cost,
        potential_profit=potential_profit,
        parts_revenue=parts_revenue,
        low_stock_value=low_stock_value,
        category_analysis=category_analysis,
        recent_part_usage=recent_part_usage
    )

@accounting_bp.route('/cost-analysis')
@login_required
def cost_analysis():
    """Detailed cost analysis for repairs and inventory"""
    if not current_user.is_admin():
        flash('You do not have permission to access this page', 'danger')
        return redirect(url_for('dashboard.index'))

    # Repair cost breakdown
    repair_costs = db.session.query(
        RepairTicket.status,
        func.avg(RepairTicket.cost).label('avg_cost'),
        func.sum(RepairTicket.cost).label('total_cost'),
        func.count(RepairTicket.id).label('count')
    ).group_by(RepairTicket.status).all()

    # Parts cost vs selling price analysis
    parts_analysis = db.session.query(
        SparePart.category,
        func.avg(SparePart.cost).label('avg_cost'),
        func.avg(SparePart.price).label('avg_price'),
        func.avg((SparePart.price - SparePart.cost) / SparePart.cost * 100).label('avg_margin')
    ).filter(SparePart.cost > 0).group_by(SparePart.category).all()

    # Monthly cost trends
    monthly_costs = db.session.query(
        func.date_trunc('month', RepairTicket.created_at).label('month'),
        func.sum(RepairTicket.cost).label('repair_costs'),
        func.sum(RepairPartUsage.quantity * SparePart.price).label('parts_costs')
    ).outerjoin(RepairPartUsage).outerjoin(SparePart).group_by(
        func.date_trunc('month', RepairTicket.created_at)
    ).order_by('month').limit(12).all()

    return render_template(
        'accounting/cost_analysis.html',
        repair_costs=repair_costs,
        parts_analysis=parts_analysis,
        monthly_costs=monthly_costs
    )

@accounting_bp.route('/financial-dashboard')
@login_required
def financial_dashboard():
    """Comprehensive financial dashboard"""
    if not current_user.is_admin():
        flash('You do not have permission to access this page', 'danger')
        return redirect(url_for('dashboard.index'))

    # Key financial metrics
    total_revenue = RepairTicket.query.filter_by(payment_status='paid').with_entities(func.sum(RepairTicket.cost)).scalar() or 0
    total_inventory_value = db.session.query(func.sum(SparePart.price * SparePart.quantity)).scalar() or 0
    pending_revenue = RepairTicket.query.filter_by(payment_status='pending').with_entities(func.sum(RepairTicket.cost)).scalar() or 0

    # Monthly trends
    current_month = datetime.now().replace(day=1)
    monthly_revenue = RepairTicket.query.filter(
        RepairTicket.payment_status == 'paid',
        RepairTicket.updated_at >= current_month
    ).with_entities(func.sum(RepairTicket.cost)).scalar() or 0

    # Profit margins
    total_parts_cost = db.session.query(func.sum(SparePart.cost * SparePart.quantity)).scalar() or 0
    inventory_profit_potential = total_inventory_value - total_parts_cost

    # Cash flow indicators
    cash_flow_data = {
        'revenue': total_revenue,
        'inventory_value': total_inventory_value,
        'pending_revenue': pending_revenue,
        'monthly_revenue': monthly_revenue,
        'inventory_profit_potential': inventory_profit_potential
    }

    return render_template(
        'accounting/financial_dashboard.html',
        cash_flow_data=cash_flow_data
    )