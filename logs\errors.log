2025-05-30 16:03:26,145 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-30T16:03:26.055267', 'status_code': 500, 'error_type': 'TemplateNotFound', 'error_message': 'whatsapp_test/instructions.html', 'url': 'http://127.0.0.1:5000/whatsapp-test/instructions', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\whatsapp_single_window_test.py", line 190, in instructions\n    return render_template(\'whatsapp_test/instructions.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 126, in load\n    source, filename, uptodate = self.get_source(environment, name)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 64, in get_source\n    return self._get_source_fast(environment, template)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 98, in _get_source_fast\n    raise TemplateNotFound(template)\njinja2.exceptions.TemplateNotFound: whatsapp_test/instructions.html\n'}
2025-05-30 16:04:36,396 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-30T16:04:36.394979', 'status_code': 500, 'error_type': 'TemplateNotFound', 'error_message': 'whatsapp_test/instructions.html', 'url': 'http://127.0.0.1:5000/whatsapp-test/instructions', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\whatsapp_single_window_test.py", line 190, in instructions\n    return render_template(\'whatsapp_test/instructions.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 126, in load\n    source, filename, uptodate = self.get_source(environment, name)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 64, in get_source\n    return self._get_source_fast(environment, template)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 98, in _get_source_fast\n    raise TemplateNotFound(template)\njinja2.exceptions.TemplateNotFound: whatsapp_test/instructions.html\n'}
2025-05-30 16:04:55,265 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-30T16:04:55.264494', 'status_code': 500, 'error_type': 'TemplateNotFound', 'error_message': 'whatsapp_test/instructions.html', 'url': 'http://127.0.0.1:5000/whatsapp-test/instructions', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\whatsapp_single_window_test.py", line 190, in instructions\n    return render_template(\'whatsapp_test/instructions.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 126, in load\n    source, filename, uptodate = self.get_source(environment, name)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 64, in get_source\n    return self._get_source_fast(environment, template)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 98, in _get_source_fast\n    raise TemplateNotFound(template)\njinja2.exceptions.TemplateNotFound: whatsapp_test/instructions.html\n'}
2025-05-30 16:05:10,908 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-30T16:05:10.907547', 'status_code': 500, 'error_type': 'TemplateNotFound', 'error_message': 'whatsapp_test/instructions.html', 'url': 'http://127.0.0.1:5000/whatsapp-test/instructions', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\whatsapp_single_window_test.py", line 190, in instructions\n    return render_template(\'whatsapp_test/instructions.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 126, in load\n    source, filename, uptodate = self.get_source(environment, name)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 64, in get_source\n    return self._get_source_fast(environment, template)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 98, in _get_source_fast\n    raise TemplateNotFound(template)\njinja2.exceptions.TemplateNotFound: whatsapp_test/instructions.html\n'}
2025-05-30 16:46:06,940 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-30T16:46:06.905032', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'whatsapp_test.index'. Did you mean 'users.index' instead?", 'url': 'http://127.0.0.1:5000/dashboard/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\dashboard.py", line 50, in index\n    return render_template(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\dashboard\\index.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 422, in top-level template code\n    href="{{ url_for(\'whatsapp_test.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'whatsapp_test.index\'. Did you mean \'users.index\' instead?\n'}
2025-05-30 16:48:37,637 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-30T16:48:37.631118', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'whatsapp_test.index'. Did you mean 'users.index' instead?", 'url': 'http://127.0.0.1:5000/dashboard/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\dashboard.py", line 50, in index\n    return render_template(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\dashboard\\index.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 422, in top-level template code\n    href="{{ url_for(\'whatsapp_test.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'whatsapp_test.index\'. Did you mean \'users.index\' instead?\n'}
2025-05-31 11:48:31,999 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T11:48:31.872920', 'status_code': 500, 'error_type': 'UndefinedError', 'error_message': "'get_locale' is undefined", 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 29, in activate_form\n    return render_template(\'activate.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\activate.html", line 1, in top-level template code\n    {% extends "base.html" %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 2, in top-level template code\n    {% set current_language = get_locale() %}\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\utils.py", line 92, in from_obj\n    if hasattr(obj, "jinja_pass_arg"):\n       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\njinja2.exceptions.UndefinedError: \'get_locale\' is undefined\n'}
2025-05-31 11:48:53,990 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T11:48:53.988166', 'status_code': 500, 'error_type': 'UndefinedError', 'error_message': "'get_locale' is undefined", 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 29, in activate_form\n    return render_template(\'activate.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\activate.html", line 1, in top-level template code\n    {% extends "base.html" %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 2, in top-level template code\n    {% set current_language = get_locale() %}\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\utils.py", line 92, in from_obj\n    if hasattr(obj, "jinja_pass_arg"):\n       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\njinja2.exceptions.UndefinedError: \'get_locale\' is undefined\n'}
2025-05-31 11:48:59,826 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T11:48:59.825535', 'status_code': 500, 'error_type': 'UndefinedError', 'error_message': "'get_locale' is undefined", 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 29, in activate_form\n    return render_template(\'activate.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\activate.html", line 1, in top-level template code\n    {% extends "base.html" %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 2, in top-level template code\n    {% set current_language = get_locale() %}\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\utils.py", line 92, in from_obj\n    if hasattr(obj, "jinja_pass_arg"):\n       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\njinja2.exceptions.UndefinedError: \'get_locale\' is undefined\n'}
2025-05-31 11:49:01,639 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T11:49:01.636691', 'status_code': 500, 'error_type': 'UndefinedError', 'error_message': "'get_locale' is undefined", 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 29, in activate_form\n    return render_template(\'activate.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\activate.html", line 1, in top-level template code\n    {% extends "base.html" %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 2, in top-level template code\n    {% set current_language = get_locale() %}\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\utils.py", line 92, in from_obj\n    if hasattr(obj, "jinja_pass_arg"):\n       ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\njinja2.exceptions.UndefinedError: \'get_locale\' is undefined\n'}
2025-05-31 14:29:32,333 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T14:29:32.244208', 'status_code': 500, 'error_type': 'TemplateSyntaxError', 'error_message': "Encountered unknown tag 'endblock'. You probably made a nesting mistake. Jinja is expecting this tag, but currently looking for 'endif'. The innermost block that needs to be closed is 'if'.", 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 25, in activate_form\n    return render_template(\'activate.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 138, in load\n    code = environment.compile(source, name, filename)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 771, in compile\n    self.handle_exception(source=source_hint)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\activate.html", line 456, in template\n    {% endblock %}\njinja2.exceptions.TemplateSyntaxError: Encountered unknown tag \'endblock\'. You probably made a nesting mistake. Jinja is expecting this tag, but currently looking for \'endif\'. The innermost block that needs to be closed is \'if\'.\n'}
2025-05-31 14:31:05,357 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T14:31:05.356505', 'status_code': 500, 'error_type': 'TemplateSyntaxError', 'error_message': "Encountered unknown tag 'endblock'. You probably made a nesting mistake. Jinja is expecting this tag, but currently looking for 'endif'. The innermost block that needs to be closed is 'if'.", 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 25, in activate_form\n    return render_template(\'activate.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 138, in load\n    code = environment.compile(source, name, filename)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 771, in compile\n    self.handle_exception(source=source_hint)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\activate.html", line 456, in template\n    {% endblock %}\njinja2.exceptions.TemplateSyntaxError: Encountered unknown tag \'endblock\'. You probably made a nesting mistake. Jinja is expecting this tag, but currently looking for \'endif\'. The innermost block that needs to be closed is \'if\'.\n'}
2025-05-31 15:11:01,021 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T15:11:00.862536', 'status_code': 500, 'error_type': 'TemplateSyntaxError', 'error_message': "Encountered unknown tag 'endblock'. You probably made a nesting mistake. Jinja is expecting this tag, but currently looking for 'endif'. The innermost block that needs to be closed is 'if'.", 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 25, in activate_form\n    return render_template(\'activate.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\loaders.py", line 138, in load\n    code = environment.compile(source, name, filename)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 771, in compile\n    self.handle_exception(source=source_hint)\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\activate.html", line 456, in template\n    {% endblock %}\njinja2.exceptions.TemplateSyntaxError: Encountered unknown tag \'endblock\'. You probably made a nesting mistake. Jinja is expecting this tag, but currently looking for \'endif\'. The innermost block that needs to be closed is \'if\'.\n'}
2025-05-31 15:26:18,407 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T15:26:18.400128', 'status_code': 500, 'error_type': 'TemplateNotFound', 'error_message': 'activate.html', 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 25, in activate_form\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 126, in load\n    source, filename, uptodate = self.get_source(environment, name)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 64, in get_source\n    return self._get_source_fast(environment, template)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 98, in _get_source_fast\n    raise TemplateNotFound(template)\njinja2.exceptions.TemplateNotFound: activate.html\n'}
2025-05-31 15:27:08,257 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T15:27:08.255769', 'status_code': 500, 'error_type': 'TemplateNotFound', 'error_message': 'activate.html', 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 25, in activate_form\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 126, in load\n    source, filename, uptodate = self.get_source(environment, name)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 64, in get_source\n    return self._get_source_fast(environment, template)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 98, in _get_source_fast\n    raise TemplateNotFound(template)\njinja2.exceptions.TemplateNotFound: activate.html\n'}
2025-05-31 15:27:45,551 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T15:27:45.550983', 'status_code': 500, 'error_type': 'TemplateNotFound', 'error_message': 'activate.html', 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 25, in activate_form\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 126, in load\n    source, filename, uptodate = self.get_source(environment, name)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 64, in get_source\n    return self._get_source_fast(environment, template)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 98, in _get_source_fast\n    raise TemplateNotFound(template)\njinja2.exceptions.TemplateNotFound: activate.html\n'}
2025-05-31 15:27:46,383 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T15:27:46.382757', 'status_code': 500, 'error_type': 'TemplateNotFound', 'error_message': 'activate.html', 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 25, in activate_form\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 126, in load\n    source, filename, uptodate = self.get_source(environment, name)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 64, in get_source\n    return self._get_source_fast(environment, template)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 98, in _get_source_fast\n    raise TemplateNotFound(template)\njinja2.exceptions.TemplateNotFound: activate.html\n'}
2025-05-31 15:30:44,152 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T15:30:44.151484', 'status_code': 500, 'error_type': 'TemplateNotFound', 'error_message': 'activate.html', 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 25, in activate_form\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 126, in load\n    source, filename, uptodate = self.get_source(environment, name)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 64, in get_source\n    return self._get_source_fast(environment, template)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 98, in _get_source_fast\n    raise TemplateNotFound(template)\njinja2.exceptions.TemplateNotFound: activate.html\n'}
2025-05-31 15:32:33,213 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T15:32:33.211053', 'status_code': 500, 'error_type': 'TemplateNotFound', 'error_message': 'activate.html', 'url': 'http://127.0.0.1:5000/activate', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 25, in activate_form\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 126, in load\n    source, filename, uptodate = self.get_source(environment, name)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 64, in get_source\n    return self._get_source_fast(environment, template)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 98, in _get_source_fast\n    raise TemplateNotFound(template)\njinja2.exceptions.TemplateNotFound: activate.html\n'}
2025-05-31 15:35:20,761 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T15:35:20.674324', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'dashboard.dashboard_home'. Did you mean 'dashboard.api_dashboard_data' instead?", 'url': 'http://127.0.0.1:5000/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 9, in index\n    return redirect(url_for(\'dashboard.dashboard_home\'))\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\helpers.py", line 225, in url_for\n    return current_app.url_for(\n           ^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'dashboard.dashboard_home\'. Did you mean \'dashboard.api_dashboard_data\' instead?\n'}
2025-05-31 15:36:11,599 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T15:36:11.587314', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'dashboard.dashboard_home'. Did you mean 'dashboard.api_dashboard_data' instead?", 'url': 'http://127.0.0.1:5000/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\main.py", line 9, in index\n    return redirect(url_for(\'dashboard.index\'))\n                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\helpers.py", line 225, in url_for\n    return current_app.url_for(\n           ^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'dashboard.dashboard_home\'. Did you mean \'dashboard.api_dashboard_data\' instead?\n'}
2025-05-31 15:37:19,080 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T15:37:19.072319', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'index'. Did you mean 'main.index' instead?", 'url': 'http://127.0.0.1:5000/auth/login?next=/dashboard/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\auth.py", line 63, in login\n    return render_template(\'auth/login.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\auth\\login.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 279, in top-level template code\n    <a class="navbar-brand animate__animated animate__fadeIn" href="{{ url_for(\'index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'index\'. Did you mean \'main.index\' instead?\n'}
2025-05-31 15:38:26,036 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T15:38:26.023196', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'index'. Did you mean 'main.index' instead?", 'url': 'http://127.0.0.1:5000/auth/login?next=/dashboard/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\auth.py", line 63, in login\n    return render_template(\'auth/login.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\auth\\login.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 279, in top-level template code\n    <a class="navbar-brand animate__animated animate__fadeIn" href="{{ url_for(\'main.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'index\'. Did you mean \'main.index\' instead?\n'}
2025-05-31 17:48:40,226 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T17:48:40.071192', 'status_code': 500, 'error_type': 'TemplateSyntaxError', 'error_message': "Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.", 'url': 'http://127.0.0.1:5000/devices/1/tickets/21', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\device.py", line 688, in show_ticket\n    return render_template(\'device/show_ticket.html\', device=device, ticket=ticket)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 138, in load\n    code = environment.compile(source, name, filename)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 771, in compile\n    self.handle_exception(source=source_hint)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\device\\show_ticket.html", line 285, in template\n    {% endblock %}\njinja2.exceptions.TemplateSyntaxError: Unexpected end of template. Jinja was looking for the following tags: \'endblock\'. The innermost block that needs to be closed is \'block\'.\n'}
2025-05-31 17:52:53,019 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T17:52:53.018841', 'status_code': 500, 'error_type': 'TemplateSyntaxError', 'error_message': "Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.", 'url': 'http://127.0.0.1:5000/devices/1/tickets/21', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\device.py", line 688, in show_ticket\n    return render_template(\'device/show_ticket.html\', device=device, ticket=ticket)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 138, in load\n    code = environment.compile(source, name, filename)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 771, in compile\n    self.handle_exception(source=source_hint)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\device\\show_ticket.html", line 285, in template\n    {% endblock %}\njinja2.exceptions.TemplateSyntaxError: Unexpected end of template. Jinja was looking for the following tags: \'endblock\'. The innermost block that needs to be closed is \'block\'.\n'}
2025-05-31 17:53:06,358 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T17:53:06.357624', 'status_code': 500, 'error_type': 'TemplateSyntaxError', 'error_message': "Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.", 'url': 'http://127.0.0.1:5000/devices/1/tickets/21', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\device.py", line 688, in show_ticket\n    return render_template(\'device/show_ticket.html\', device=device, ticket=ticket)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 138, in load\n    code = environment.compile(source, name, filename)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 771, in compile\n    self.handle_exception(source=source_hint)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\device\\show_ticket.html", line 285, in template\n    {% endblock %}\njinja2.exceptions.TemplateSyntaxError: Unexpected end of template. Jinja was looking for the following tags: \'endblock\'. The innermost block that needs to be closed is \'block\'.\n'}
2025-05-31 17:55:31,243 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T17:55:31.242885', 'status_code': 500, 'error_type': 'TemplateSyntaxError', 'error_message': "Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.", 'url': 'http://127.0.0.1:5000/devices/1/tickets/21', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\device.py", line 688, in show_ticket\n    return render_template(\'device/show_ticket.html\', device=device, ticket=ticket)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 138, in load\n    code = environment.compile(source, name, filename)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 771, in compile\n    self.handle_exception(source=source_hint)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\device\\show_ticket.html", line 285, in template\n    {% endblock %}\njinja2.exceptions.TemplateSyntaxError: Unexpected end of template. Jinja was looking for the following tags: \'endblock\'. The innermost block that needs to be closed is \'block\'.\n'}
2025-05-31 17:55:49,899 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T17:55:49.895781', 'status_code': 500, 'error_type': 'TemplateSyntaxError', 'error_message': "Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.", 'url': 'http://127.0.0.1:5000/devices/1/tickets/21', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\device.py", line 688, in show_ticket\n    return render_template(\'device/show_ticket.html\', device=device, ticket=ticket)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\loaders.py", line 138, in load\n    code = environment.compile(source, name, filename)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 771, in compile\n    self.handle_exception(source=source_hint)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\device\\show_ticket.html", line 308, in template\n    {% endblock %}\njinja2.exceptions.TemplateSyntaxError: Unexpected end of template. Jinja was looking for the following tags: \'endblock\'. The innermost block that needs to be closed is \'block\'.\n'}
2025-05-31 17:59:15,624 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T17:59:15.442887', 'status_code': 500, 'error_type': 'TemplateSyntaxError', 'error_message': "Unexpected end of template. Jinja was looking for the following tags: 'endblock'. The innermost block that needs to be closed is 'block'.", 'url': 'http://127.0.0.1:5000/devices/1/tickets/21', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\device.py", line 688, in show_ticket\n    return render_template(\'device/show_ticket.html\', device=device, ticket=ticket)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\loaders.py", line 138, in load\n    code = environment.compile(source, name, filename)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 771, in compile\n    self.handle_exception(source=source_hint)\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\device\\show_ticket.html", line 308, in template\n    {% endblock %}\njinja2.exceptions.TemplateSyntaxError: Unexpected end of template. Jinja was looking for the following tags: \'endblock\'. The innermost block that needs to be closed is \'block\'.\n'}
2025-05-31 18:21:56,693 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T18:21:56.600607', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'device.get_cost_suggestions' with values ['device_id', 'ticket_id']. Did you mean 'device_api.get_device_statistics' instead?", 'url': 'http://127.0.0.1:5000/devices/1/tickets/21', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\device.py", line 688, in show_ticket\n    return render_template(\'device/show_ticket.html\', device=device, ticket=ticket)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\device\\show_ticket.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 605, in top-level template code\n    {% block scripts %}{% endblock %}\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\templates\\device\\show_ticket.html", line 587, in block \'scripts\'\n    fetch(\'{{ url_for("device.get_cost_suggestions", device_id=device.id, ticket_id=ticket.id) }}\')\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'device.get_cost_suggestions\' with values [\'device_id\', \'ticket_id\']. Did you mean \'device_api.get_device_statistics\' instead?\n'}
2025-05-31 18:23:53,167 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T18:23:53.160612', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'device.get_cost_suggestions' with values ['device_id', 'ticket_id']. Did you mean 'device_api.get_device_statistics' instead?", 'url': 'http://127.0.0.1:5000/devices/1/tickets/21', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\device.py", line 688, in show_ticket\n    return render_template(\'device/show_ticket.html\', device=device, ticket=ticket)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\device\\show_ticket.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 605, in top-level template code\n    {% block scripts %}{% endblock %}\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\templates\\device\\show_ticket.html", line 587, in block \'scripts\'\n    fetch(\'{{ url_for("device.get_cost_suggestions", device_id=device.id, ticket_id=ticket.id) }}\')\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'device.get_cost_suggestions\' with values [\'device_id\', \'ticket_id\']. Did you mean \'device_api.get_device_statistics\' instead?\n'}
2025-05-31 22:14:45,387 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T22:14:45.303076', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'whatsapp_test.index'. Did you mean 'users.index' instead?", 'url': 'http://127.0.0.1:5000/dashboard/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\dashboard.py", line 50, in index\n    return render_template(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\dashboard\\index.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 441, in top-level template code\n    href="{{ url_for(\'whatsapp_test.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'whatsapp_test.index\'. Did you mean \'users.index\' instead?\n'}
2025-05-31 22:15:20,637 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T22:15:20.630047', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'whatsapp_test.index'. Did you mean 'users.index' instead?", 'url': 'http://127.0.0.1:5000/dashboard/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\dashboard.py", line 50, in index\n    return render_template(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\dashboard\\index.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 441, in top-level template code\n    href="{{ url_for(\'whatsapp_test.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'whatsapp_test.index\'. Did you mean \'users.index\' instead?\n'}
2025-05-31 22:18:25,806 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T22:18:25.799577', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'whatsapp_test.index'. Did you mean 'users.index' instead?", 'url': 'http://127.0.0.1:5000/dashboard/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\dashboard.py", line 50, in index\n    return render_template(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\dashboard\\index.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 441, in top-level template code\n    href="{{ url_for(\'whatsapp_test.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'whatsapp_test.index\'. Did you mean \'users.index\' instead?\n'}
2025-05-31 22:19:59,895 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T22:19:59.888961', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'whatsapp_test.index'. Did you mean 'users.index' instead?", 'url': 'http://127.0.0.1:5000/notifications/user/preferences', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\notifications.py", line 139, in user_preferences\n    return render_template(\'notifications/user_preferences.html\', user_prefs=user_prefs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\notifications\\user_preferences.html", line 1, in top-level template code\n    {% extends "base.html" %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 441, in top-level template code\n    href="{{ url_for(\'whatsapp_test.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'whatsapp_test.index\'. Did you mean \'users.index\' instead?\n'}
2025-05-31 22:20:06,391 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T22:20:06.381673', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'whatsapp_test.index'. Did you mean 'users.index' instead?", 'url': 'http://127.0.0.1:5000/dashboard/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\dashboard.py", line 50, in index\n    return render_template(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\dashboard\\index.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 441, in top-level template code\n    href="{{ url_for(\'whatsapp_test.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'whatsapp_test.index\'. Did you mean \'users.index\' instead?\n'}
2025-05-31 22:20:07,653 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T22:20:07.644220', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'whatsapp_test.index'. Did you mean 'users.index' instead?", 'url': 'http://127.0.0.1:5000/dashboard/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\dashboard.py", line 50, in index\n    return render_template(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\dashboard\\index.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 441, in top-level template code\n    href="{{ url_for(\'whatsapp_test.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'whatsapp_test.index\'. Did you mean \'users.index\' instead?\n'}
2025-05-31 22:20:25,395 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T22:20:25.390486', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'whatsapp_test.index'. Did you mean 'users.index' instead?", 'url': 'http://127.0.0.1:5000/dashboard/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\dashboard.py", line 50, in index\n    return render_template(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\dashboard\\index.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 441, in top-level template code\n    href="{{ url_for(\'whatsapp_test.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'whatsapp_test.index\'. Did you mean \'users.index\' instead?\n'}
2025-05-31 22:20:37,101 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T22:20:37.088795', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'whatsapp_test.index'. Did you mean 'users.index' instead?", 'url': 'http://127.0.0.1:5000/devices/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\device.py", line 98, in index\n    return render_template(\'device/index.html\', devices=devices, dashboard_stats=dashboard_stats)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\device\\index.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 441, in top-level template code\n    href="{{ url_for(\'whatsapp_test.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'whatsapp_test.index\'. Did you mean \'users.index\' instead?\n'}
2025-05-31 22:21:33,456 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T22:21:33.449075', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'whatsapp_test.index'. Did you mean 'users.index' instead?", 'url': 'http://127.0.0.1:5000/dashboard/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\dashboard.py", line 50, in index\n    return render_template(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\dashboard\\index.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 441, in top-level template code\n    href="{{ url_for(\'whatsapp_test.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'whatsapp_test.index\'. Did you mean \'users.index\' instead?\n'}
2025-05-31 22:24:21,999 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-05-31T22:24:21.991333', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'whatsapp_test.index'. Did you mean 'users.index' instead?", 'url': 'http://127.0.0.1:5000/technician/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\technician.py", line 19, in index\n    return render_template(\'technician/index.html\', technicians=technicians)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\technician\\index.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 441, in top-level template code\n    href="{{ url_for(\'whatsapp_test.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'whatsapp_test.index\'. Did you mean \'users.index\' instead?\n'}
2025-06-01 13:44:32,807 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-01T13:44:32.669081', 'status_code': 500, 'error_type': 'InvalidRequestError', 'error_message': "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[Customer(customer)]'. Original exception was: Could not determine join condition between parent/child tables on relationship Customer.devices - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a 'primaryjoin' expression.", 'url': 'http://localhost:5000/auth/register', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\auth.py", line 80, in register\n    if User.query.count() > 0:\n       ^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_sqlalchemy\\model.py", line 30, in __get__\n    return cls.query_class(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 276, in __init__\n    self._set_entities(entities)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 289, in <listcomp>\n    coercions.expect(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\sql\\coercions.py", line 406, in expect\n    insp._post_inspect\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py", line 1260, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2707, in _post_inspect\n    self._check_configure()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2386, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4199, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4236, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can\'t proceed with initialization of other mappers. Triggering mapper: \'Mapper[Customer(customer)]\'. Original exception was: Could not determine join condition between parent/child tables on relationship Customer.devices - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a \'primaryjoin\' expression.\n'}
2025-06-01 13:45:16,718 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-01T13:45:16.716846', 'status_code': 500, 'error_type': 'InvalidRequestError', 'error_message': "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[Customer(customer)]'. Original exception was: Could not determine join condition between parent/child tables on relationship Customer.devices - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a 'primaryjoin' expression.", 'url': 'http://localhost:5000/auth/login', 'method': 'POST', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\auth.py", line 27, in login\n    user = User.query.filter_by(username=username).first()\n           ^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_sqlalchemy\\model.py", line 30, in __get__\n    return cls.query_class(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 276, in __init__\n    self._set_entities(entities)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 289, in <listcomp>\n    coercions.expect(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\sql\\coercions.py", line 406, in expect\n    insp._post_inspect\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py", line 1260, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2707, in _post_inspect\n    self._check_configure()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2386, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4199, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4236, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can\'t proceed with initialization of other mappers. Triggering mapper: \'Mapper[Customer(customer)]\'. Original exception was: Could not determine join condition between parent/child tables on relationship Customer.devices - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a \'primaryjoin\' expression.\n'}
2025-06-01 13:45:30,611 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-01T13:45:30.610393', 'status_code': 500, 'error_type': 'InvalidRequestError', 'error_message': "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[Customer(customer)]'. Original exception was: Could not determine join condition between parent/child tables on relationship Customer.devices - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a 'primaryjoin' expression.", 'url': 'http://localhost:5000/auth/login', 'method': 'POST', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\auth.py", line 27, in login\n    user = User.query.filter_by(username=username).first()\n           ^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_sqlalchemy\\model.py", line 30, in __get__\n    return cls.query_class(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 276, in __init__\n    self._set_entities(entities)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 289, in <listcomp>\n    coercions.expect(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\sql\\coercions.py", line 406, in expect\n    insp._post_inspect\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py", line 1260, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2707, in _post_inspect\n    self._check_configure()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2386, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4199, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4236, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can\'t proceed with initialization of other mappers. Triggering mapper: \'Mapper[Customer(customer)]\'. Original exception was: Could not determine join condition between parent/child tables on relationship Customer.devices - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a \'primaryjoin\' expression.\n'}
2025-06-01 13:45:34,077 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-01T13:45:34.076035', 'status_code': 500, 'error_type': 'InvalidRequestError', 'error_message': "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[Customer(customer)]'. Original exception was: Could not determine join condition between parent/child tables on relationship Customer.devices - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a 'primaryjoin' expression.", 'url': 'http://localhost:5000/auth/register', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\auth.py", line 80, in register\n    if User.query.count() > 0:\n       ^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_sqlalchemy\\model.py", line 30, in __get__\n    return cls.query_class(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 276, in __init__\n    self._set_entities(entities)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 289, in <listcomp>\n    coercions.expect(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\sql\\coercions.py", line 406, in expect\n    insp._post_inspect\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py", line 1260, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2707, in _post_inspect\n    self._check_configure()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2386, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4199, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4236, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can\'t proceed with initialization of other mappers. Triggering mapper: \'Mapper[Customer(customer)]\'. Original exception was: Could not determine join condition between parent/child tables on relationship Customer.devices - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a \'primaryjoin\' expression.\n'}
2025-06-01 14:00:14,759 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-01T14:00:14.751066', 'status_code': 500, 'error_type': 'InvalidRequestError', 'error_message': "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[User(user)]'. Original exception was: Could not determine join condition between parent/child tables on relationship User.devices - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a 'primaryjoin' expression.", 'url': 'http://127.0.0.1:5000/auth/login?next=/dashboard/', 'method': 'POST', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\auth.py", line 27, in login\n    user = User.query.filter_by(username=username).first()\n           ^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_sqlalchemy\\model.py", line 30, in __get__\n    return cls.query_class(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 276, in __init__\n    self._set_entities(entities)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 289, in <listcomp>\n    coercions.expect(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\sql\\coercions.py", line 406, in expect\n    insp._post_inspect\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py", line 1260, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2707, in _post_inspect\n    self._check_configure()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2386, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4199, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4236, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can\'t proceed with initialization of other mappers. Triggering mapper: \'Mapper[User(user)]\'. Original exception was: Could not determine join condition between parent/child tables on relationship User.devices - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a \'primaryjoin\' expression.\n'}
2025-06-01 14:00:26,095 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-01T14:00:26.094447', 'status_code': 500, 'error_type': 'InvalidRequestError', 'error_message': "One or more mappers failed to initialize - can't proceed with initialization of other mappers. Triggering mapper: 'Mapper[User(user)]'. Original exception was: Could not determine join condition between parent/child tables on relationship User.devices - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a 'primaryjoin' expression.", 'url': 'http://127.0.0.1:5000/auth/register', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\auth.py", line 80, in register\n    if User.query.count() > 0:\n       ^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_sqlalchemy\\model.py", line 30, in __get__\n    return cls.query_class(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 276, in __init__\n    self._set_entities(entities)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 288, in _set_entities\n    self._raw_columns = [\n                        ^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 289, in <listcomp>\n    coercions.expect(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\sql\\coercions.py", line 406, in expect\n    insp._post_inspect\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\util\\langhelpers.py", line 1260, in __get__\n    obj.__dict__[self.__name__] = result = self.fget(obj)\n                                           ^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2707, in _post_inspect\n    self._check_configure()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 2386, in _check_configure\n    _configure_registries({self.registry}, cascade=True)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4199, in _configure_registries\n    _do_configure_registries(registries, cascade)\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\mapper.py", line 4236, in _do_configure_registries\n    raise e\nsqlalchemy.exc.InvalidRequestError: One or more mappers failed to initialize - can\'t proceed with initialization of other mappers. Triggering mapper: \'Mapper[User(user)]\'. Original exception was: Could not determine join condition between parent/child tables on relationship User.devices - there are no foreign keys linking these tables.  Ensure that referencing columns are associated with a ForeignKey or ForeignKeyConstraint, or specify a \'primaryjoin\' expression.\n'}
2025-06-01 14:35:06,559 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-01T14:35:06.316548', 'status_code': 500, 'error_type': 'OperationalError', 'error_message': '(sqlite3.OperationalError) database is locked\n[SQL: SELECT user.id AS user_id, user.tenant_id AS user_tenant_id, user.username AS user_username, user.email AS user_email, user.password_hash AS user_password_hash, user.full_name AS user_full_name, user.phone AS user_phone, user.role AS user_role, user.created_at AS user_created_at, user.last_login AS user_last_login, user.is_active AS user_is_active, user.address AS user_address, user.position AS user_position, user.department AS user_department, user.email_verified AS user_email_verified, user.email_verification_sent_at AS user_email_verification_sent_at, user.telegram_chat_id AS user_telegram_chat_id, user.notifications_enabled AS user_notifications_enabled, user.notify_new_device AS user_notify_new_device, user.notify_status_change AS user_notify_status_change, user.notify_repair_complete AS user_notify_repair_complete, user.language_preference AS user_language_preference, user.trial_start_date AS user_trial_start_date, user.trial_end_date AS user_trial_end_date, user.is_trial_account AS user_is_trial_account, user.trial_expired AS user_trial_expired, user.license_type AS user_license_type \nFROM user \nWHERE user.id = ?]\n[parameters: (17,)]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)', 'url': 'http://127.0.0.1:5000/accounting/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1969, in _exec_single_context\n    self.dialect.do_execute(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\default.py", line 922, in do_execute\n    cursor.execute(statement, parameters)\nsqlite3.OperationalError: database is locked\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 284, in decorated_view\n    elif not current_user.is_authenticated:\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\local.py", line 311, in __get__\n    obj = instance._get_current_object()\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\local.py", line 515, in _get_current_object\n    return get_name(local())\n                    ^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 25, in <lambda>\n    current_user = LocalProxy(lambda: _get_user())\n                                      ^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 370, in _get_user\n    current_app.login_manager._load_user()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\login_manager.py", line 364, in _load_user\n    user = self._user_callback(user_id)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\models\\user.py", line 200, in load_user\n    return User.query.get(int(user_id))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "<string>", line 2, in get\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\util\\deprecations.py", line 386, in warned\n    return fn(*args, **kwargs)  # type: ignore[no-any-return]\n           ^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 1136, in get\n    return self._get_impl(ident, loading.load_on_pk_identity)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 1145, in _get_impl\n    return self.session._get_impl(\n           ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 3833, in _get_impl\n    return db_load_fn(\n           ^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\loading.py", line 690, in load_on_pk_identity\n    session.execute(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 2308, in execute\n    return self._execute_internal(\n           ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 2190, in _execute_internal\n    result: Result[Any] = compile_state_cls.orm_execute_statement(\n                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\context.py", line 293, in orm_execute_statement\n    result = conn.execute(\n             ^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1416, in execute\n    return meth(\n           ^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\sql\\elements.py", line 516, in _execute_on_connection\n    return connection._execute_clauseelement(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1639, in _execute_clauseelement\n    ret = self._execute_context(\n          ^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1848, in _execute_context\n    return self._exec_single_context(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1988, in _exec_single_context\n    self._handle_dbapi_exception(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 2343, in _handle_dbapi_exception\n    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1969, in _exec_single_context\n    self.dialect.do_execute(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\default.py", line 922, in do_execute\n    cursor.execute(statement, parameters)\nsqlalchemy.exc.OperationalError: (sqlite3.OperationalError) database is locked\n[SQL: SELECT user.id AS user_id, user.tenant_id AS user_tenant_id, user.username AS user_username, user.email AS user_email, user.password_hash AS user_password_hash, user.full_name AS user_full_name, user.phone AS user_phone, user.role AS user_role, user.created_at AS user_created_at, user.last_login AS user_last_login, user.is_active AS user_is_active, user.address AS user_address, user.position AS user_position, user.department AS user_department, user.email_verified AS user_email_verified, user.email_verification_sent_at AS user_email_verification_sent_at, user.telegram_chat_id AS user_telegram_chat_id, user.notifications_enabled AS user_notifications_enabled, user.notify_new_device AS user_notify_new_device, user.notify_status_change AS user_notify_status_change, user.notify_repair_complete AS user_notify_repair_complete, user.language_preference AS user_language_preference, user.trial_start_date AS user_trial_start_date, user.trial_end_date AS user_trial_end_date, user.is_trial_account AS user_is_trial_account, user.trial_expired AS user_trial_expired, user.license_type AS user_license_type \nFROM user \nWHERE user.id = ?]\n[parameters: (17,)]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\n'}
2025-06-01 14:35:08,746 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-01T14:35:08.742052', 'status_code': 500, 'error_type': 'OperationalError', 'error_message': '(sqlite3.OperationalError) database is locked\n[SQL: SELECT user.id AS user_id, user.tenant_id AS user_tenant_id, user.username AS user_username, user.email AS user_email, user.password_hash AS user_password_hash, user.full_name AS user_full_name, user.phone AS user_phone, user.role AS user_role, user.created_at AS user_created_at, user.last_login AS user_last_login, user.is_active AS user_is_active, user.address AS user_address, user.position AS user_position, user.department AS user_department, user.email_verified AS user_email_verified, user.email_verification_sent_at AS user_email_verification_sent_at, user.telegram_chat_id AS user_telegram_chat_id, user.notifications_enabled AS user_notifications_enabled, user.notify_new_device AS user_notify_new_device, user.notify_status_change AS user_notify_status_change, user.notify_repair_complete AS user_notify_repair_complete, user.language_preference AS user_language_preference, user.trial_start_date AS user_trial_start_date, user.trial_end_date AS user_trial_end_date, user.is_trial_account AS user_is_trial_account, user.trial_expired AS user_trial_expired, user.license_type AS user_license_type \nFROM user \nWHERE user.id = ?]\n[parameters: (17,)]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)', 'url': 'http://127.0.0.1:5000/accounting/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1969, in _exec_single_context\n    self.dialect.do_execute(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\default.py", line 922, in do_execute\n    cursor.execute(statement, parameters)\nsqlite3.OperationalError: database is locked\n\nThe above exception was the direct cause of the following exception:\n\nTraceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 284, in decorated_view\n    elif not current_user.is_authenticated:\n             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\local.py", line 311, in __get__\n    obj = instance._get_current_object()\n          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\local.py", line 515, in _get_current_object\n    return get_name(local())\n                    ^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 25, in <lambda>\n    current_user = LocalProxy(lambda: _get_user())\n                                      ^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 370, in _get_user\n    current_app.login_manager._load_user()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\login_manager.py", line 364, in _load_user\n    user = self._user_callback(user_id)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\models\\user.py", line 200, in load_user\n    return User.query.get(int(user_id))\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "<string>", line 2, in get\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\util\\deprecations.py", line 386, in warned\n    return fn(*args, **kwargs)  # type: ignore[no-any-return]\n           ^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 1136, in get\n    return self._get_impl(ident, loading.load_on_pk_identity)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\query.py", line 1145, in _get_impl\n    return self.session._get_impl(\n           ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 3833, in _get_impl\n    return db_load_fn(\n           ^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\loading.py", line 690, in load_on_pk_identity\n    session.execute(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 2308, in execute\n    return self._execute_internal(\n           ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\session.py", line 2190, in _execute_internal\n    result: Result[Any] = compile_state_cls.orm_execute_statement(\n                          ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\orm\\context.py", line 293, in orm_execute_statement\n    result = conn.execute(\n             ^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1416, in execute\n    return meth(\n           ^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\sql\\elements.py", line 516, in _execute_on_connection\n    return connection._execute_clauseelement(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1639, in _execute_clauseelement\n    ret = self._execute_context(\n          ^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1848, in _execute_context\n    return self._exec_single_context(\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1988, in _exec_single_context\n    self._handle_dbapi_exception(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 2343, in _handle_dbapi_exception\n    raise sqlalchemy_exception.with_traceback(exc_info[2]) from e\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\base.py", line 1969, in _exec_single_context\n    self.dialect.do_execute(\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\sqlalchemy\\engine\\default.py", line 922, in do_execute\n    cursor.execute(statement, parameters)\nsqlalchemy.exc.OperationalError: (sqlite3.OperationalError) database is locked\n[SQL: SELECT user.id AS user_id, user.tenant_id AS user_tenant_id, user.username AS user_username, user.email AS user_email, user.password_hash AS user_password_hash, user.full_name AS user_full_name, user.phone AS user_phone, user.role AS user_role, user.created_at AS user_created_at, user.last_login AS user_last_login, user.is_active AS user_is_active, user.address AS user_address, user.position AS user_position, user.department AS user_department, user.email_verified AS user_email_verified, user.email_verification_sent_at AS user_email_verification_sent_at, user.telegram_chat_id AS user_telegram_chat_id, user.notifications_enabled AS user_notifications_enabled, user.notify_new_device AS user_notify_new_device, user.notify_status_change AS user_notify_status_change, user.notify_repair_complete AS user_notify_repair_complete, user.language_preference AS user_language_preference, user.trial_start_date AS user_trial_start_date, user.trial_end_date AS user_trial_end_date, user.is_trial_account AS user_is_trial_account, user.trial_expired AS user_trial_expired, user.license_type AS user_license_type \nFROM user \nWHERE user.id = ?]\n[parameters: (17,)]\n(Background on this error at: https://sqlalche.me/e/20/e3q8)\n'}
2025-06-01 16:11:53,852 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-01T16:11:53.730739', 'status_code': 500, 'error_type': 'TemplateNotFound', 'error_message': 'employee/add.html', 'url': 'http://localhost:5000/employee/add', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\employee_management.py", line 99, in add_employee\n    return render_template(\'employee/add.html\')\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\templating.py", line 150, in render_template\n    template = app.jinja_env.get_or_select_template(template_name_or_list)\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 1087, in get_or_select_template\n    return self.get_template(template_name_or_list, parent, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 1016, in get_template\n    return self._load_template(name, globals)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\environment.py", line 975, in _load_template\n    template = self.loader.load(self, name, self.make_globals(globals))\n               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\jinja2\\loaders.py", line 126, in load\n    source, filename, uptodate = self.get_source(environment, name)\n                                 ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\templating.py", line 64, in get_source\n    return self._get_source_fast(environment, template)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\.venv\\Lib\\site-packages\\flask\\templating.py", line 98, in _get_source_fast\n    raise TemplateNotFound(template)\njinja2.exceptions.TemplateNotFound: employee/add.html\n'}
2025-06-02 17:16:40,066 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-02T17:16:39.971989', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'simple_inventory.index'. Did you mean 'enhanced_simple_inventory.index' instead?", 'url': 'http://127.0.0.1:5000/dashboard/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1484, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1469, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 290, in decorated_view\n    return current_app.ensure_sync(func)(*args, **kwargs)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "E:\\device repair manaement system - Copy\\app\\routes\\dashboard.py", line 47, in index\n    return render_template(\n           ^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 151, in render_template\n    return _render(app, template, context)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\templating.py", line 132, in _render\n    rv = template.render(context)\n         ^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 1295, in render\n    self.environment.handle_exception()\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\jinja2\\environment.py", line 942, in handle_exception\n    raise rewrite_traceback_stack(source=source)\n  File "E:\\device repair manaement system - Copy\\app\\templates\\dashboard\\index.html", line 1, in top-level template code\n    {% extends \'base.html\' %}\n  File "E:\\device repair manaement system - Copy\\app\\templates\\base.html", line 392, in top-level template code\n    href="{{ url_for(\'simple_inventory.index\') }}">\n    ^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1697, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\flask\\app.py", line 1686, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 950, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'simple_inventory.index\'. Did you mean \'enhanced_simple_inventory.index\' instead?\n'}
2025-06-04 04:41:13,614 - device_repair_system - ERROR - Server Error: {'timestamp': '2025-06-04T04:41:13.608905', 'status_code': 500, 'error_type': 'BuildError', 'error_message': "Could not build url for endpoint 'auth.login'. Did you mean 'users.index' instead?", 'url': 'http://127.0.0.1:5000/inventory/', 'method': 'GET', 'user_agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36', 'ip_address': '127.0.0.1', 'user_id': 'Anonymous', 'traceback': 'Traceback (most recent call last):\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask\\app.py", line 917, in full_dispatch_request\n    rv = self.dispatch_request()\n         ^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask\\app.py", line 902, in dispatch_request\n    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 285, in decorated_view\n    return current_app.login_manager.unauthorized()\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask_login\\login_manager.py", line 196, in unauthorized\n    redirect_url = make_login_url(login_view, next_url=request.url)\n                   ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 120, in login_url\n    base = expand_login_view(login_view)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask_login\\utils.py", line 97, in expand_login_view\n    return url_for(login_view)\n           ^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask\\helpers.py", line 232, in url_for\n    return current_app.url_for(\n           ^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask\\app.py", line 1121, in url_for\n    return self.handle_url_build_error(error, endpoint, values)\n           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\flask\\app.py", line 1110, in url_for\n    rv = url_adapter.build(  # type: ignore[union-attr]\n         ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^\n  File "C:\\Program Files\\Python311\\Lib\\site-packages\\werkzeug\\routing\\map.py", line 924, in build\n    raise BuildError(endpoint, values, method, self)\nwerkzeug.routing.exceptions.BuildError: Could not build url for endpoint \'auth.login\'. Did you mean \'users.index\' instead?\n'}
