#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
🔄 DATABASE MIGRATION TO MARIADB
================================
Comprehensive migration script to move from SQLite to MariaDB
"""

import os
import sys
import sqlite3
import pymysql
import json
from datetime import datetime
from urllib.parse import quote_plus
import logging

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('migration.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class DatabaseMigrator:
    """Handles migration from SQLite to MariaDB"""
    
    def __init__(self):
        # MariaDB configuration
        self.mariadb_config = {
            'host': os.environ.get('MARIADB_HOST', 'localhost'),
            'port': int(os.environ.get('MARIADB_PORT', '3308')),
            'user': os.environ.get('MARIADB_USER', 'repair_admin'),
            'password': os.environ.get('MARIADB_PASSWORD', 'MMAA@68906742'),
            'database': os.environ.get('MARIADB_DATABASE', 'device_repair_management_system'),
            'charset': 'utf8mb4'
        }
        
        # SQLite database path
        self.sqlite_path = 'instance/app.db'
        
        # Find the best SQLite database
        self._find_best_sqlite_db()
        
    def _find_best_sqlite_db(self):
        """Find the most complete SQLite database"""
        possible_dbs = [
            'instance/app.db',
            'instance/app_fixed.db',
            'instance/consolidated_system.db',
            'instance/integrated_system.db'
        ]
        
        best_db = None
        max_tables = 0
        
        for db_path in possible_dbs:
            if os.path.exists(db_path):
                try:
                    conn = sqlite3.connect(db_path)
                    cursor = conn.cursor()
                    cursor.execute("SELECT name FROM sqlite_master WHERE type='table'")
                    tables = cursor.fetchall()
                    table_count = len(tables)
                    conn.close()
                    
                    logger.info(f"Database {db_path}: {table_count} tables")
                    
                    if table_count > max_tables:
                        max_tables = table_count
                        best_db = db_path
                        
                except Exception as e:
                    logger.warning(f"Could not read {db_path}: {e}")
        
        if best_db:
            self.sqlite_path = best_db
            logger.info(f"Selected source database: {best_db} ({max_tables} tables)")
        else:
            logger.error("No valid SQLite database found!")
            sys.exit(1)
    
    def test_mariadb_connection(self):
        """Test MariaDB connection"""
        try:
            conn = pymysql.connect(**self.mariadb_config)
            logger.info("✅ MariaDB connection successful")
            conn.close()
            return True
        except Exception as e:
            logger.error(f"❌ MariaDB connection failed: {e}")
            return False
    
    def create_mariadb_database(self):
        """Create MariaDB database if it doesn't exist"""
        try:
            # Connect without specifying database
            config = self.mariadb_config.copy()
            database_name = config.pop('database')
            
            conn = pymysql.connect(**config)
            cursor = conn.cursor()
            
            # Create database
            cursor.execute(f"CREATE DATABASE IF NOT EXISTS `{database_name}` CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            cursor.execute(f"USE `{database_name}`")
            
            logger.info(f"✅ Database '{database_name}' created/verified")
            conn.close()
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to create database: {e}")
            return False
    
    def get_sqlite_schema(self):
        """Extract schema from SQLite database"""
        try:
            conn = sqlite3.connect(self.sqlite_path)
            cursor = conn.cursor()
            
            # Get all tables
            cursor.execute("SELECT name FROM sqlite_master WHERE type='table' AND name NOT LIKE 'sqlite_%'")
            tables = [row[0] for row in cursor.fetchall()]
            
            schema = {}
            for table in tables:
                cursor.execute(f"PRAGMA table_info({table})")
                columns = cursor.fetchall()
                schema[table] = columns
                
            conn.close()
            logger.info(f"✅ Extracted schema for {len(tables)} tables")
            return schema, tables
            
        except Exception as e:
            logger.error(f"❌ Failed to extract SQLite schema: {e}")
            return {}, []
    
    def convert_sqlite_to_mariadb_type(self, sqlite_type):
        """Convert SQLite data types to MariaDB equivalents"""
        type_mapping = {
            'INTEGER': 'INT',
            'TEXT': 'TEXT',
            'REAL': 'DOUBLE',
            'BLOB': 'LONGBLOB',
            'NUMERIC': 'DECIMAL(10,2)',
            'VARCHAR': 'VARCHAR',
            'DATETIME': 'DATETIME',
            'DATE': 'DATE',
            'TIME': 'TIME',
            'BOOLEAN': 'TINYINT(1)',
            'FLOAT': 'FLOAT'
        }
        
        sqlite_type = sqlite_type.upper()
        
        # Handle VARCHAR with length
        if 'VARCHAR(' in sqlite_type:
            return sqlite_type
        
        # Handle specific patterns
        for sqlite_key, mariadb_type in type_mapping.items():
            if sqlite_key in sqlite_type:
                return mariadb_type
        
        # Default fallback
        return 'TEXT'
    
    def create_mariadb_tables(self, schema):
        """Create tables in MariaDB based on SQLite schema"""
        try:
            conn = pymysql.connect(**self.mariadb_config)
            cursor = conn.cursor()
            
            # Disable foreign key checks during creation
            cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
            
            for table_name, columns in schema.items():
                # Build CREATE TABLE statement
                column_defs = []
                
                for col in columns:
                    col_name = col[1]
                    col_type = self.convert_sqlite_to_mariadb_type(col[2])
                    not_null = "NOT NULL" if col[3] else ""
                    default_val = f"DEFAULT {col[4]}" if col[4] is not None else ""
                    primary_key = "PRIMARY KEY AUTO_INCREMENT" if col[5] else ""
                    
                    column_def = f"`{col_name}` {col_type} {not_null} {default_val} {primary_key}".strip()
                    column_defs.append(column_def)
                
                create_sql = f"""
                CREATE TABLE IF NOT EXISTS `{table_name}` (
                    {', '.join(column_defs)}
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
                """
                
                cursor.execute(create_sql)
                logger.info(f"✅ Created table: {table_name}")
            
            # Re-enable foreign key checks
            cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
            conn.commit()
            conn.close()
            
            logger.info(f"✅ Created {len(schema)} tables in MariaDB")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to create MariaDB tables: {e}")
            return False
    
    def migrate_data(self, tables):
        """Migrate data from SQLite to MariaDB"""
        try:
            # Connect to both databases
            sqlite_conn = sqlite3.connect(self.sqlite_path)
            sqlite_conn.row_factory = sqlite3.Row  # Enable column access by name
            
            mariadb_conn = pymysql.connect(**self.mariadb_config)
            mariadb_cursor = mariadb_conn.cursor()
            
            # Disable foreign key checks during migration
            mariadb_cursor.execute("SET FOREIGN_KEY_CHECKS = 0")
            
            total_rows = 0
            
            for table in tables:
                logger.info(f"Migrating table: {table}")
                
                # Get data from SQLite
                sqlite_cursor = sqlite_conn.cursor()
                sqlite_cursor.execute(f"SELECT * FROM {table}")
                rows = sqlite_cursor.fetchall()
                
                if not rows:
                    logger.info(f"  No data in table {table}")
                    continue
                
                # Get column names
                column_names = [description[0] for description in sqlite_cursor.description]
                
                # Prepare INSERT statement
                placeholders = ', '.join(['%s'] * len(column_names))
                columns_str = ', '.join([f"`{col}`" for col in column_names])
                insert_sql = f"INSERT INTO `{table}` ({columns_str}) VALUES ({placeholders})"
                
                # Insert data in batches
                batch_size = 1000
                for i in range(0, len(rows), batch_size):
                    batch = rows[i:i + batch_size]
                    batch_data = [tuple(row) for row in batch]
                    
                    try:
                        mariadb_cursor.executemany(insert_sql, batch_data)
                        mariadb_conn.commit()
                    except Exception as e:
                        logger.warning(f"  Error inserting batch in {table}: {e}")
                        # Try individual inserts
                        for row in batch_data:
                            try:
                                mariadb_cursor.execute(insert_sql, row)
                                mariadb_conn.commit()
                            except Exception as row_error:
                                logger.warning(f"  Skipped row in {table}: {row_error}")
                
                total_rows += len(rows)
                logger.info(f"  Migrated {len(rows)} rows from {table}")
            
            # Re-enable foreign key checks
            mariadb_cursor.execute("SET FOREIGN_KEY_CHECKS = 1")
            
            sqlite_conn.close()
            mariadb_conn.close()
            
            logger.info(f"✅ Migration completed! Total rows migrated: {total_rows}")
            return True
            
        except Exception as e:
            logger.error(f"❌ Data migration failed: {e}")
            return False
    
    def verify_migration(self, tables):
        """Verify migration by comparing row counts"""
        try:
            sqlite_conn = sqlite3.connect(self.sqlite_path)
            mariadb_conn = pymysql.connect(**self.mariadb_config)
            
            sqlite_cursor = sqlite_conn.cursor()
            mariadb_cursor = mariadb_conn.cursor()
            
            verification_results = {}
            
            for table in tables:
                # Count rows in SQLite
                sqlite_cursor.execute(f"SELECT COUNT(*) FROM {table}")
                sqlite_count = sqlite_cursor.fetchone()[0]
                
                # Count rows in MariaDB
                mariadb_cursor.execute(f"SELECT COUNT(*) FROM `{table}`")
                mariadb_count = mariadb_cursor.fetchone()[0]
                
                verification_results[table] = {
                    'sqlite_count': sqlite_count,
                    'mariadb_count': mariadb_count,
                    'match': sqlite_count == mariadb_count
                }
                
                status = "✅" if sqlite_count == mariadb_count else "❌"
                logger.info(f"{status} {table}: SQLite={sqlite_count}, MariaDB={mariadb_count}")
            
            sqlite_conn.close()
            mariadb_conn.close()
            
            # Summary
            total_tables = len(verification_results)
            successful_tables = sum(1 for result in verification_results.values() if result['match'])
            
            logger.info(f"Verification Summary: {successful_tables}/{total_tables} tables match")
            
            return verification_results
            
        except Exception as e:
            logger.error(f"❌ Verification failed: {e}")
            return {}
    
    def run_migration(self):
        """Run the complete migration process"""
        logger.info("🚀 Starting database migration to MariaDB")
        
        # Step 1: Test MariaDB connection
        if not self.test_mariadb_connection():
            return False
        
        # Step 2: Create database
        if not self.create_mariadb_database():
            return False
        
        # Step 3: Extract SQLite schema
        schema, tables = self.get_sqlite_schema()
        if not schema:
            return False
        
        # Step 4: Create MariaDB tables
        if not self.create_mariadb_tables(schema):
            return False
        
        # Step 5: Migrate data
        if not self.migrate_data(tables):
            return False
        
        # Step 6: Verify migration
        verification = self.verify_migration(tables)
        
        # Step 7: Update configuration
        self.update_config_for_mariadb()
        
        logger.info("🎉 Migration completed successfully!")
        return True
    
    def update_config_for_mariadb(self):
        """Update configuration to use MariaDB"""
        try:
            # Set environment variable
            os.environ['DATABASE_TYPE'] = 'mariadb'
            
            # Create .env file
            env_content = f"""# Database Configuration
DATABASE_TYPE=mariadb
MARIADB_HOST={self.mariadb_config['host']}
MARIADB_PORT={self.mariadb_config['port']}
MARIADB_USER={self.mariadb_config['user']}
MARIADB_PASSWORD={self.mariadb_config['password']}
MARIADB_DATABASE={self.mariadb_config['database']}
"""
            
            with open('.env', 'w') as f:
                f.write(env_content)
            
            logger.info("✅ Configuration updated for MariaDB")
            
        except Exception as e:
            logger.warning(f"⚠️ Could not update configuration: {e}")

def main():
    """Main migration function"""
    print("🔄 Database Migration to MariaDB")
    print("=" * 50)
    
    migrator = DatabaseMigrator()
    
    # Confirm migration
    response = input("Are you sure you want to migrate to MariaDB? (yes/no): ")
    if response.lower() != 'yes':
        print("Migration cancelled.")
        return
    
    # Run migration
    success = migrator.run_migration()
    
    if success:
        print("\n🎉 Migration completed successfully!")
        print("The application will now use MariaDB as the primary database.")
    else:
        print("\n❌ Migration failed. Check the logs for details.")
        print("The application will continue using SQLite.")

if __name__ == '__main__':
    main()
