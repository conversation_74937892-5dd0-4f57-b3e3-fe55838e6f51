"""
Enhanced Activation Guard V2
Improved reliability, security, and error handling for activation system
"""

from flask import request, redirect, url_for, session, current_app, jsonify, flash
from functools import wraps
import sqlite3
import logging
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, Tuple, List
from app.utils.system_info import get_machine_id

# Configure logging
logger = logging.getLogger(__name__)


class ActivationGuardV2:
    """Enhanced activation guard with improved reliability and security"""
    
    # Routes that are allowed without activation
    ALLOWED_ROUTES = {
        'main.activate_form',
        'main.activate_code',
        'main.index',
        'static',
        'public_api_activate',
        'public_system_status',
        'server_status',
        'auth.login',          # Allow login page
        'auth.register'        # Allow registration
    }
    
    # API paths that are allowed without activation
    ALLOWED_API_PATHS = {
        '/api/activate',
        '/api/system-status',
        '/status',
        '/api/admin/generate-codes'  # Allow code generation
    }
    
    # Static file extensions
    ALLOWED_STATIC_EXTENSIONS = {
        '.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.ico', '.svg', 
        '.woff', '.woff2', '.ttf', '.eot', '.map', '.json'
    }
    
    # Cache for activation status to reduce database hits
    _activation_cache = {
        'status': None,
        'timestamp': None,
        'cache_duration': 300  # 5 minutes
    }
    
    @classmethod
    def get_database_path(cls) -> str:
        """Get the correct database path"""
        # Try consolidated database first, then fallback to app_fixed.db
        paths = ['instance/app.db', 'instance/app_fixed.db']
        
        for path in paths:
            try:
                if sqlite3.connect(path).execute("SELECT 1").fetchone():
                    return path
            except:
                continue
                
        # Default to consolidated database
        return 'instance/app.db'
    
    @classmethod
    def is_system_activated(cls, force_refresh: bool = False) -> bool:
        """
        Check if the system is activated for this machine

        Args:
            force_refresh: Force refresh of cache

        Returns:
            bool: True if system is activated
        """
        # 🔓 ACTIVATION SYSTEM TEMPORARILY DISABLED
        # Return True to bypass activation requirements
        return True

        # Original activation check code (commented out for temporary bypass)
        """
        try:
            # Check cache first (unless force refresh)
            if not force_refresh and cls._is_cache_valid():
                cached_status = cls._activation_cache.get('status')
                if cached_status is not None:
                    return cached_status

            machine_id = get_machine_id()
            if not machine_id:
                logger.warning("Machine ID not available")
                return False

            # Check database for active activation
            db_path = cls.get_database_path()
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()

            # Enhanced query with better error handling
            cursor.execute('''
                SELECT id, code, expiry_date, activation_date, is_active, created_at
                FROM activation
                WHERE client_id = ? AND is_active = 1
                AND datetime(expiry_date) > datetime('now')
                ORDER BY activation_date DESC
                LIMIT 1
            ''', (machine_id,))

            result = cursor.fetchone()
            conn.close()

            is_activated = result is not None

            # Update cache
            cls._update_cache(is_activated)

            # Update session if available
            cls._update_session(result)

            logger.info(f"Activation check: {'ACTIVE' if is_activated else 'INACTIVE'} for machine {machine_id}")
            return is_activated

        except sqlite3.Error as e:
            logger.error(f"Database error during activation check: {e}")
            return False
        except Exception as e:
            logger.error(f"Unexpected error during activation check: {e}")
            return False
        """
    
    @classmethod
    def _is_cache_valid(cls) -> bool:
        """Check if activation cache is still valid"""
        if not cls._activation_cache['timestamp']:
            return False
            
        cache_age = (datetime.now() - cls._activation_cache['timestamp']).total_seconds()
        return cache_age < cls._activation_cache['cache_duration']
    
    @classmethod
    def _update_cache(cls, status: bool):
        """Update activation cache"""
        cls._activation_cache.update({
            'status': status,
            'timestamp': datetime.now()
        })
    
    @classmethod
    def _update_session(cls, activation_result: Optional[Tuple]):
        """Update session with activation information"""
        try:
            if activation_result:
                session.update({
                    'activation_verified': True,
                    'activation_code': activation_result[1],
                    'activation_expiry': activation_result[2],
                    'activation_date': activation_result[3]
                })
            else:
                # Clear stale session data
                for key in ['activation_verified', 'activation_code', 'activation_expiry', 'activation_date']:
                    session.pop(key, None)
        except RuntimeError:
            # Session not available (e.g., outside request context)
            pass
    
    @classmethod
    def is_route_allowed(cls, endpoint: Optional[str]) -> bool:
        """Check if route is allowed without activation"""
        if not endpoint:
            return False
            
        # Check allowed routes
        if endpoint in cls.ALLOWED_ROUTES:
            return True
            
        # Check static files
        if endpoint == 'static' or (endpoint and endpoint.startswith('static')):
            return True
            
        return False
    
    @classmethod
    def is_static_file(cls, path: str) -> bool:
        """Check if request is for a static file"""
        if not path:
            return False
            
        # Check file extensions
        for ext in cls.ALLOWED_STATIC_EXTENSIONS:
            if path.lower().endswith(ext):
                return True
                
        # Check static paths
        static_paths = ['/static/', '/favicon.ico', '/robots.txt', '/apple-touch-icon']
        return any(static_path in path for static_path in static_paths)
    
    @classmethod
    def before_request(cls):
        """Activation guard disabled - system no longer requires activation"""
        # Activation system has been removed - allow all requests
        return None
    
    @classmethod
    def get_activation_info(cls) -> Dict[str, Any]:
        """Get comprehensive activation information"""
        try:
            machine_id = get_machine_id()
            if not machine_id:
                return {'is_active': False, 'error': 'Machine ID not available'}
            
            db_path = cls.get_database_path()
            conn = sqlite3.connect(db_path)
            cursor = conn.cursor()
            
            # Get current activation
            cursor.execute("""
                SELECT code, expiry_date, activation_date, is_active, created_at
                FROM activation 
                WHERE client_id = ? AND is_active = 1 
                AND datetime(expiry_date) > datetime('now')
                ORDER BY activation_date DESC 
                LIMIT 1
            """, (machine_id,))
            
            result = cursor.fetchone()
            
            if not result:
                conn.close()
                return {'is_active': False, 'message': 'No active activation found'}
            
            # Calculate remaining time
            expiry_date = datetime.fromisoformat(result[1].replace('Z', '+00:00'))
            now = datetime.now()
            remaining_time = expiry_date - now
            remaining_days = remaining_time.days
            remaining_hours = remaining_time.seconds // 3600
            
            # Determine plan type
            plan_type = cls._determine_plan_type(result[0], remaining_days)
            
            # Get usage statistics
            cursor.execute("SELECT COUNT(*) FROM device WHERE tenant_id = 1")
            device_count = cursor.fetchone()[0]
            
            cursor.execute("SELECT COUNT(*) FROM user WHERE role != 'customer'")
            staff_count = cursor.fetchone()[0]
            
            conn.close()
            
            return {
                'is_active': True,
                'code': result[0],
                'plan_type': plan_type,
                'expiry_date': result[1],
                'activation_date': result[2],
                'remaining_days': max(0, remaining_days),
                'remaining_hours': remaining_hours if remaining_days == 0 else None,
                'created_at': result[4],
                'machine_id': machine_id,
                'usage': {
                    'devices': device_count,
                    'staff': staff_count
                },
                'status': cls._get_activation_status(remaining_days)
            }
            
        except Exception as e:
            logger.error(f"Error getting activation info: {e}")
            return {'is_active': False, 'error': str(e)}
    
    @classmethod
    def _determine_plan_type(cls, code: str, remaining_days: int) -> str:
        """Determine plan type from activation code and duration"""
        code_upper = code.upper()
        
        # Check code patterns first
        if any(pattern in code_upper for pattern in ['DEMO7', 'FREE', 'TRIAL']):
            return 'Free Trial'
        elif any(pattern in code_upper for pattern in ['DEMO30', 'BASIC']):
            return 'Basic'
        elif any(pattern in code_upper for pattern in ['DEMO90', 'STANDARD']):
            return 'Standard'
        elif any(pattern in code_upper for pattern in ['DEMO365', 'PREMIUM', 'ADMIN']):
            return 'Premium'
        
        # Fallback to duration-based detection
        if remaining_days <= 7:
            return 'Free Trial'
        elif remaining_days <= 30:
            return 'Basic'
        elif remaining_days <= 90:
            return 'Standard'
        else:
            return 'Premium'
    
    @classmethod
    def _get_activation_status(cls, remaining_days: int) -> str:
        """Get activation status based on remaining days"""
        if remaining_days <= 0:
            return 'expired'
        elif remaining_days <= 3:
            return 'expiring_soon'
        elif remaining_days <= 7:
            return 'expiring'
        else:
            return 'active'
    
    @classmethod
    def invalidate_cache(cls):
        """Invalidate activation cache (useful after activation changes)"""
        cls._activation_cache.update({
            'status': None,
            'timestamp': None
        })
        logger.info("Activation cache invalidated")


def activation_required(f):
    """Activation decorator disabled - system no longer requires activation"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # Activation system has been removed - allow all requests
        return f(*args, **kwargs)
    return decorated_function


# Convenience functions for backward compatibility
def get_activation_info() -> Dict[str, Any]:
    """Get activation information (backward compatibility)"""
    return ActivationGuardV2.get_activation_info()


def is_system_activated() -> bool:
    """Check if system is activated (backward compatibility)"""
    return ActivationGuardV2.is_system_activated()
