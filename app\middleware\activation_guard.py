"""
Mandatory Activation Middleware
Blocks all access to application features until valid activation code is entered
"""

from flask import request, redirect, url_for, session, current_app
from functools import wraps
import sqlite3
from datetime import datetime
from app.utils.system_info import get_machine_id


class ActivationGuard:
    """Middleware to enforce mandatory activation"""

    # Routes that are allowed without activation
    ALLOWED_ROUTES = {
        'main.activate_form',
        'main.activate_code',
        'static',
        'main.index',  # Allow index to redirect to activation
        'public_api_activate',  # API endpoint for activation
        'public_system_status',  # System status endpoint
        'server_status'  # Server status endpoint
    }

    # API paths that are allowed without activation
    ALLOWED_API_PATHS = {
        '/api/activate',
        '/api/system-status',
        '/status'
    }

    # Static file extensions that are always allowed
    ALLOWED_STATIC_EXTENSIONS = {'.css', '.js', '.png', '.jpg', '.jpeg', '.gif', '.ico', '.svg', '.woff', '.woff2', '.ttf'}

    @staticmethod
    def is_system_activated():
        """Check if the system is activated for this machine"""
        # 🔓 ACTIVATION SYSTEM TEMPORARILY DISABLED
        # Return True to bypass activation requirements
        return True

        # Original activation check code (commented out for temporary bypass)
        """
        try:
            machine_id = get_machine_id()

            # If machine_id is None or empty, system is not activated
            if not machine_id:
                return False

            # Check database for active activation
            conn = sqlite3.connect('instance/app_fixed.db')
            cursor = conn.cursor()

            cursor.execute('''
                SELECT id, code, expiry_date, activation_date, is_active
                FROM activation
                WHERE client_id = ? AND is_active = 1 AND expiry_date > datetime('now')
                ORDER BY activation_date DESC
                LIMIT 1
            ''', (machine_id,))

            result = cursor.fetchone()
            conn.close()

            if result:
                # Store activation info in session for quick access
                try:
                    session['activation_verified'] = True
                    session['activation_code'] = result[1]
                    session['activation_expiry'] = result[2]
                    session['activation_date'] = result[3]
                except:
                    # If session is not available, still return True
                    pass
                return True
            else:
                # Clear any stale session data
                try:
                    session.pop('activation_verified', None)
                    session.pop('activation_code', None)
                    session.pop('activation_expiry', None)
                    session.pop('activation_date', None)
                except:
                    # If session is not available, ignore
                    pass
                return False

        except Exception as e:
            # Log error only if we have app context
            try:
                current_app.logger.error(f"Error checking activation status: {e}")
            except:
                # No app context, just print to console
                print(f"Activation check error: {e}")
            return False
        """

    @staticmethod
    def is_route_allowed(endpoint):
        """Check if route is allowed without activation"""
        if not endpoint:
            return False

        # Allow specific routes
        if endpoint in ActivationGuard.ALLOWED_ROUTES:
            return True

        # Allow static files
        if endpoint == 'static' or endpoint.startswith('static'):
            return True

        return False

    @staticmethod
    def is_static_file(path):
        """Check if request is for a static file"""
        if not path:
            return False

        # Check for static file extensions
        for ext in ActivationGuard.ALLOWED_STATIC_EXTENSIONS:
            if path.lower().endswith(ext):
                return True

        # Check for static paths
        static_paths = ['/static/', '/favicon.ico', '/robots.txt']
        for static_path in static_paths:
            if static_path in path:
                return True

        return False

    @staticmethod
    def before_request():
        """Middleware function to run before each request"""
        # Skip activation check for static files
        if ActivationGuard.is_static_file(request.path):
            return None

        # Skip activation check for allowed API paths
        if request.path in ActivationGuard.ALLOWED_API_PATHS:
            return None

        # Skip activation check for allowed routes
        if ActivationGuard.is_route_allowed(request.endpoint):
            return None

        # Check if system is activated
        if not ActivationGuard.is_system_activated():
            # Redirect to activation page
            if request.endpoint != 'main.activate_form':
                return redirect(url_for('main.activate_form'))

        return None


def activation_required(f):
    """Decorator to require activation for specific routes"""
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if not ActivationGuard.is_system_activated():
            return redirect(url_for('main.activate_form'))
        return f(*args, **kwargs)
    return decorated_function


def get_activation_info():
    """Get current activation information"""
    try:
        machine_id = get_machine_id()

        conn = sqlite3.connect('instance/app_fixed.db')
        cursor = conn.cursor()

        cursor.execute("""
            SELECT code, expiry_date, activation_date, is_active, created_at
            FROM activation
            WHERE client_id = ? AND is_active = 1 AND expiry_date > datetime('now')
            ORDER BY activation_date DESC
            LIMIT 1
        """, (machine_id,))

        result = cursor.fetchone()
        conn.close()

        if result:
            # Calculate remaining days
            expiry_date = datetime.fromisoformat(result[1].replace('Z', '+00:00'))
            remaining_days = (expiry_date - datetime.now()).days

            # Determine subscription type based on code pattern
            code = result[0]
            if 'DEMO7' in code or 'FREE' in code:
                plan_type = 'Free Trial'
            elif 'DEMO30' in code or 'BASIC' in code:
                plan_type = 'Basic'
            elif 'DEMO90' in code or 'STANDARD' in code:
                plan_type = 'Standard'
            elif 'DEMO365' in code or 'PREMIUM' in code:
                plan_type = 'Premium'
            else:
                # Default based on duration
                if remaining_days <= 7:
                    plan_type = 'Free Trial'
                elif remaining_days <= 30:
                    plan_type = 'Basic'
                elif remaining_days <= 90:
                    plan_type = 'Standard'
                else:
                    plan_type = 'Premium'

            return {
                'is_active': True,
                'code': code,
                'plan_type': plan_type,
                'expiry_date': result[1],
                'activation_date': result[2],
                'remaining_days': max(0, remaining_days),
                'created_at': result[4]
            }

        return {'is_active': False}

    except Exception as e:
        current_app.logger.error(f"Error getting activation info: {e}")
        return {'is_active': False}
