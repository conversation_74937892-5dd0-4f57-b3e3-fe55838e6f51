# Device Repair Management System

A Flask-based application for managing device repairs and customer relationships.

## Features

- **Device Repair Management**: Track repair status, technician assignments, and repair history
- **Customer Management**: Maintain customer profiles and repair history
- **Staff Management**: Manage technicians and staff members
- **Accounting Integration**: Track repair costs and revenue
- **Multi-tenant Support**: Support for multiple repair shops
- **Notifications**: SMS and email notifications for repair status updates
- **Reports**: Generate detailed reports on repairs and business performance
- **API Integration**: Integration with external systems and APIs
- **Mobile Responsive**: Works on desktop and mobile devices

## Telegram Notifications

The system includes Telegram notifications for:
- Account creation
- Password resets/changes
- Device repair ticket creation
- Device drop-off confirmation to customers
- New device assignments to technicians
- Repair status updates to customers
- Device ready for pickup notifications to customers

### Telegram Bot Configuration

The system is configured with the following Telegram bot:
- Bot username: @coredesk1
- Bot ID: **********
- First name: Core
- Last name: desk
- Token: **********:AAE62MY4oD7wUvBYNxWxT5WNfEYGPwXTrOA
- Language: Arabic

### How to Receive Telegram Notifications

To receive notifications from the repair management system:

1. Start a chat with the bot on Telegram: https://t.me/coredesk1
2. Get your Telegram chat ID using the /start command with the bot
3. Go to your profile in the repair management system
4. Add your Telegram chat ID in the profile settings
5. Save your profile

## Email Configuration

For email verification and password reset to work, you need to configure your email settings:

1. Copy `.env-example` to `.env`
2. Update the email configuration section with your SMTP server details
3. If using Gmail, you'll need to create an app password
4. Restart the application

## Default Accounts

- Admin: admin / admin123
- Technician: technician / tech123
- Customer: customer / customer123

## Installation

1. Clone the repository
2. Create a virtual environment: `python -m venv venv`
3. Activate the virtual environment:
   - Windows: `venv\Scripts\activate`
   - Linux/Mac: `source venv/bin/activate`
4. Install requirements: `pip install -r requirements.txt`
5. Copy `.env-example` to `.env` and configure settings
6. Run the application: `python app.py`