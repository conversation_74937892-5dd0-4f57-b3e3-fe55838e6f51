import os
from datetime import timedelta
from urllib.parse import quote_plus

class Config:
    # Flask configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'device-repair-system-2025-secure'

    # Database configuration - Auto-detect MariaDB or fallback to SQLite
    DATABASE_TYPE = os.environ.get('DATABASE_TYPE', 'auto')  # auto, mariadb, sqlite

    # MariaDB configuration
    MARIADB_HOST = os.environ.get('MARIADB_HOST', 'localhost')
    MARIADB_PORT = int(os.environ.get('MARIADB_PORT', '3308'))
    MARIADB_USER = os.environ.get('MARIADB_USER', 'repair_admin')
    MARIADB_PASSWORD = os.environ.get('MARIADB_PASSWORD', 'MMAA@68906742')
    MARIADB_DATABASE = os.environ.get('MARIADB_DATABASE', 'device_repair_management_system')

    # Auto-detect database type
    def _detect_database_type(self):
        """Auto-detect available database"""
        if self.DATABASE_TYPE == 'mariadb':
            return 'mariadb'
        elif self.DATABASE_TYPE == 'sqlite':
            return 'sqlite'
        else:  # auto-detect
            try:
                import pymysql
                # Try to connect to MariaDB
                conn = pymysql.connect(
                    host=self.MARIADB_HOST,
                    port=self.MARIADB_PORT,
                    user=self.MARIADB_USER,
                    password=self.MARIADB_PASSWORD,
                    connect_timeout=5
                )
                conn.close()
                return 'mariadb'
            except:
                return 'sqlite'

    # Database URI - Auto-switching
    @property
    def database_uri(self):
        """Get database URI based on configuration"""
        db_type = self._detect_database_type()

        if db_type == 'mariadb':
            return (
                f'mysql+pymysql://{self.MARIADB_USER}:{quote_plus(self.MARIADB_PASSWORD)}'
                f'@{self.MARIADB_HOST}:{self.MARIADB_PORT}/{self.MARIADB_DATABASE}'
                f'?charset=utf8mb4&autocommit=true&connect_timeout=10'
            )
        else:
            # SQLite fallback
            return 'sqlite:///' + os.path.join(os.path.abspath(os.path.dirname(__file__)), 'instance', 'app.db')

    # Set the database URI dynamically
    @property
    def SQLALCHEMY_DATABASE_URI(self):
        return self.database_uri
    
    @property
    def SQLALCHEMY_ENGINE_OPTIONS(self):
        """Get engine options based on database type"""
        db_type = self._detect_database_type()

        if db_type == 'mariadb':
            return {
                'pool_pre_ping': True,
                'pool_recycle': 300,
                'pool_timeout': 20,
                'max_overflow': 0,
                'pool_size': 10,
                'echo': False,
                'connect_args': {
                    'charset': 'utf8mb4',
                    'autocommit': True,
                    'connect_timeout': 10,
                    'read_timeout': 30,
                    'write_timeout': 30
                }
            }
        else:
            return {
                'pool_pre_ping': True,
                'pool_recycle': 300,
                'echo': False,
                'connect_args': {
                    'check_same_thread': False,
                    'timeout': 20
                }
            }
    
    SQLALCHEMY_TRACK_MODIFICATIONS = False

    # Session configuration
    PERMANENT_SESSION_LIFETIME = timedelta(days=1)

    # Application configuration
    APP_NAME = 'Device Repair Management System'
    APP_URL = os.environ.get('APP_URL', 'http://localhost:5000')

    # Subscription configuration
    TRIAL_DAYS = 14
    SUBSCRIPTION_PLANS = {
        'basic': {
            'name': 'Basic',
            'price_monthly': 3.99,
            'price_yearly': 39.90,
            'max_devices': 50,
            'max_technicians': 1,
            'features': [
                'تتبع الأجهزة الأساسي',
                'دعم البريد الإلكتروني',
                'التقارير الأساسية',
                'إدارة المخزون الأساسية'
            ]
        },
        'standard': {
            'name': 'Standard',
            'price_monthly': 6.99,
            'price_yearly': 69.90,
            'max_devices': 200,
            'max_technicians': 3,
            'features': [
                'تتبع الأجهزة المتقدم',
                'دعم البريد الإلكتروني والواتساب',
                'التقارير المتقدمة',
                'إدارة المخزون المتقدمة',
                'دعم العملاء المباشر'
            ]
        },
        'premium': {
            'name': 'Premium',
            'price_monthly': 9.99,
            'price_yearly': 99.90,
            'max_devices': 500,
            'max_technicians': 5,
            'features': [
                'تتبع الأجهزة غير المحدود',
                'دعم متعدد القنوات',
                'تقارير مخصصة',
                'إدارة المخزون المتقدمة',
                'دعم العملاء المباشر 24/7',
                'خيارات العلامة التجارية المخصصة'
            ]
        }
    }

    # Logging configuration
    LOG_LEVEL = os.environ.get('LOG_LEVEL', 'INFO')
    LOG_FILE = os.environ.get('LOG_FILE', 'app.log')

    @staticmethod
    def init_app(app):
        """Initialize application configuration."""
        import logging
        from logging.handlers import RotatingFileHandler

        handler = RotatingFileHandler(
            Config.LOG_FILE,
            maxBytes=10000000,  # 10MB
            backupCount=10
        )
        handler.setFormatter(logging.Formatter(
            '%(asctime)s %(levelname)s: %(message)s '
            '[in %(pathname)s:%(lineno)d]'
        ))
        handler.setLevel(logging.INFO)
        app.logger.addHandler(handler)
        app.logger.setLevel(logging.INFO)
        app.logger.info('Device Repair Management System startup')

class DevelopmentConfig(Config):
    DEBUG = True
    SQLALCHEMY_ECHO = False
    SESSION_COOKIE_SECURE = False

class TestingConfig(Config):
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///:memory:'
    WTF_CSRF_ENABLED = False

class ProductionConfig(Config):
    DEBUG = False
    SQLALCHEMY_ECHO = False

config = {
    'development': DevelopmentConfig,
    'testing': TestingConfig,
    'production': ProductionConfig,
    'default': DevelopmentConfig
}
