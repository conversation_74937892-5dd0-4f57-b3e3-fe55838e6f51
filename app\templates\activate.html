{% extends 'base.html' %}

{% block title %}System Activation{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row justify-content-center">
        <div class="col-md-6">
            <div class="card shadow">
                <div class="card-header bg-primary text-white text-center">
                    <h4><i class="fas fa-key me-2"></i>System Activation</h4>
                </div>
                <div class="card-body">
                    <!-- Activation Disabled Notice -->
                    <div class="alert alert-info text-center">
                        <i class="fas fa-info-circle fa-2x mb-3"></i>
                        <h5>Activation System Temporarily Disabled</h5>
                        <p class="mb-0">The system is currently running in open access mode. No activation code is required.</p>
                    </div>
                    
                    <div class="text-center">
                        <a href="{{ url_for('dashboard.index') }}" class="btn btn-primary btn-lg">
                            <i class="fas fa-tachometer-alt me-2"></i>Go to Dashboard
                        </a>
                    </div>
                    
                    <!-- Original Activation Form (Hidden) -->
                    <div class="d-none">
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show">
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}

                        <form method="POST" action="{{ url_for('main.activate_code') }}">
                            <div class="mb-3">
                                <label for="activation_code" class="form-label">
                                    <i class="fas fa-key me-1"></i>Activation Code
                                </label>
                                <input type="text" 
                                       class="form-control form-control-lg text-center" 
                                       id="activation_code" 
                                       name="activation_code" 
                                       placeholder="Enter your activation code"
                                       style="letter-spacing: 2px; font-family: monospace;"
                                       required>
                                <div class="form-text">
                                    Enter the activation code provided by your administrator
                                </div>
                            </div>
                            
                            <div class="d-grid">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-unlock me-2"></i>Activate System
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
                
                <div class="card-footer text-center text-muted">
                    <small>
                        <i class="fas fa-shield-alt me-1"></i>
                        Device Repair Management System
                    </small>
                </div>
            </div>
            
            <!-- System Information -->
            <div class="card mt-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-info-circle me-2"></i>System Information</h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="border-end">
                                <i class="fas fa-server text-primary fa-2x mb-2"></i>
                                <h6>Status</h6>
                                <span class="badge bg-success">Online</span>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="border-end">
                                <i class="fas fa-database text-info fa-2x mb-2"></i>
                                <h6>Database</h6>
                                <span class="badge bg-info">Connected</span>
                            </div>
                        </div>
                        <div class="col-4">
                            <i class="fas fa-unlock text-success fa-2x mb-2"></i>
                            <h6>Access</h6>
                            <span class="badge bg-success">Open</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.card {
    border: none;
    border-radius: 15px;
}

.card-header {
    border-radius: 15px 15px 0 0 !important;
}

.btn-lg {
    padding: 12px 30px;
    border-radius: 25px;
}

.form-control-lg {
    border-radius: 10px;
}

.alert {
    border-radius: 10px;
}

.badge {
    font-size: 0.8em;
}

.border-end {
    border-right: 1px solid #dee2e6 !important;
}

@media (max-width: 768px) {
    .border-end {
        border-right: none !important;
        border-bottom: 1px solid #dee2e6 !important;
        margin-bottom: 1rem;
        padding-bottom: 1rem;
    }
    
    .col-4:last-child .border-end {
        border-bottom: none !important;
        margin-bottom: 0;
        padding-bottom: 0;
    }
}
</style>
{% endblock %}
