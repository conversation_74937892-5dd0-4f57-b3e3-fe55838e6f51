#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
📦 ENHANCED INVENTORY MANAGEMENT ROUTES
======================================
Comprehensive inventory management with search, filtering, and accounting integration
"""

from flask import Blueprint, render_template, request, jsonify, flash, redirect, url_for
from flask_login import login_required, current_user
from sqlalchemy import or_, and_, desc, func, text
from datetime import datetime, timedelta
import json

from app.extensions import db
from app.models.spare_part import SparePart, StockMovement, RepairPartUsage
from app.models.spare_parts_enhanced import SaleTransaction, SaleTransactionItem, InventoryAudit, ReorderAlert
from app.decorators.auth import admin_required

inventory_enhanced_bp = Blueprint('inventory_enhanced', __name__)

@inventory_enhanced_bp.route('/')
@login_required
def index():
    """Enhanced inventory dashboard with comprehensive overview"""
    
    # Get filter parameters
    search = request.args.get('search', '').strip()
    category = request.args.get('category', '')
    brand = request.args.get('brand', '')
    min_price = request.args.get('min_price', type=float)
    max_price = request.args.get('max_price', type=float)
    stock_status = request.args.get('stock_status', '')
    sort_by = request.args.get('sort_by', 'name')
    sort_order = request.args.get('sort_order', 'asc')
    page = request.args.get('page', 1, type=int)
    per_page = request.args.get('per_page', 20, type=int)
    
    # Build query
    query = SparePart.query
    
    # Apply filters
    if search:
        search_filter = or_(
            SparePart.name.ilike(f'%{search}%'),
            SparePart.part_number.ilike(f'%{search}%'),
            SparePart.description.ilike(f'%{search}%'),
            SparePart.brand.ilike(f'%{search}%'),
            SparePart.category.ilike(f'%{search}%')
        )
        query = query.filter(search_filter)
    
    if category:
        query = query.filter(SparePart.category == category)
    
    if brand:
        query = query.filter(SparePart.brand == brand)
    
    if min_price is not None:
        query = query.filter(SparePart.price >= min_price)
    
    if max_price is not None:
        query = query.filter(SparePart.price <= max_price)
    
    if stock_status:
        if stock_status == 'in_stock':
            query = query.filter(SparePart.quantity > 0)
        elif stock_status == 'low_stock':
            query = query.filter(and_(SparePart.quantity > 0, SparePart.quantity <= SparePart.min_quantity))
        elif stock_status == 'out_of_stock':
            query = query.filter(SparePart.quantity == 0)
    
    # Apply sorting
    if sort_by == 'name':
        order_col = SparePart.name
    elif sort_by == 'price':
        order_col = SparePart.price
    elif sort_by == 'quantity':
        order_col = SparePart.quantity
    elif sort_by == 'category':
        order_col = SparePart.category
    elif sort_by == 'brand':
        order_col = SparePart.brand
    elif sort_by == 'created_at':
        order_col = SparePart.created_at
    else:
        order_col = SparePart.name
    
    if sort_order == 'desc':
        order_col = desc(order_col)
    
    query = query.order_by(order_col)
    
    # Paginate results
    pagination = query.paginate(
        page=page, 
        per_page=per_page, 
        error_out=False
    )
    
    spare_parts = pagination.items
    
    # Get filter options
    categories = db.session.query(SparePart.category).distinct().filter(SparePart.category.isnot(None)).all()
    categories = [cat[0] for cat in categories if cat[0]]
    
    brands = db.session.query(SparePart.brand).distinct().filter(SparePart.brand.isnot(None)).all()
    brands = [brand[0] for brand in brands if brand[0]]
    
    # Get summary statistics
    total_parts = SparePart.query.count()
    total_value = db.session.query(func.sum(SparePart.price * SparePart.quantity)).scalar() or 0
    low_stock_count = SparePart.query.filter(
        and_(SparePart.quantity > 0, SparePart.quantity <= SparePart.min_quantity)
    ).count()
    out_of_stock_count = SparePart.query.filter(SparePart.quantity == 0).count()
    
    # Recent stock movements
    recent_movements = StockMovement.query.order_by(desc(StockMovement.created_at)).limit(10).all()
    
    # Low stock alerts
    low_stock_items = SparePart.query.filter(
        and_(SparePart.quantity > 0, SparePart.quantity <= SparePart.min_quantity)
    ).order_by(SparePart.quantity).limit(10).all()
    
    return render_template(
        'inventory/enhanced_index.html',
        spare_parts=spare_parts,
        pagination=pagination,
        categories=categories,
        brands=brands,
        search=search,
        category=category,
        brand=brand,
        min_price=min_price,
        max_price=max_price,
        stock_status=stock_status,
        sort_by=sort_by,
        sort_order=sort_order,
        per_page=per_page,
        total_parts=total_parts,
        total_value=total_value,
        low_stock_count=low_stock_count,
        out_of_stock_count=out_of_stock_count,
        recent_movements=recent_movements,
        low_stock_items=low_stock_items
    )

@inventory_enhanced_bp.route('/api/search')
@login_required
def api_search():
    """API endpoint for real-time search"""
    query = request.args.get('q', '').strip()
    limit = request.args.get('limit', 10, type=int)
    
    if not query:
        return jsonify([])
    
    # Search spare parts
    search_filter = or_(
        SparePart.name.ilike(f'%{query}%'),
        SparePart.part_number.ilike(f'%{query}%'),
        SparePart.brand.ilike(f'%{query}%'),
        SparePart.category.ilike(f'%{query}%')
    )
    
    parts = SparePart.query.filter(search_filter).limit(limit).all()
    
    results = []
    for part in parts:
        results.append({
            'id': part.id,
            'name': part.name,
            'part_number': part.part_number,
            'brand': part.brand,
            'category': part.category,
            'price': float(part.price),
            'quantity': part.quantity,
            'stock_status': 'in_stock' if part.quantity > part.min_quantity else 'low_stock' if part.quantity > 0 else 'out_of_stock'
        })
    
    return jsonify(results)

@inventory_enhanced_bp.route('/part/<int:part_id>')
@login_required
def part_detail(part_id):
    """Detailed view of a specific part"""
    part = SparePart.query.get_or_404(part_id)
    
    # Get stock movement history
    movements = StockMovement.query.filter_by(spare_part_id=part_id).order_by(desc(StockMovement.created_at)).limit(20).all()
    
    # Get usage in repairs
    repair_usage = RepairPartUsage.query.filter_by(spare_part_id=part_id).order_by(desc(RepairPartUsage.created_at)).limit(10).all()
    
    # Get sales history
    sales = db.session.query(SaleTransactionItem).join(SaleTransaction).filter(
        SaleTransactionItem.spare_part_id == part_id
    ).order_by(desc(SaleTransaction.created_at)).limit(10).all()
    
    return render_template(
        'inventory/part_detail.html',
        part=part,
        movements=movements,
        repair_usage=repair_usage,
        sales=sales
    )

@inventory_enhanced_bp.route('/add', methods=['GET', 'POST'])
@login_required
@admin_required
def add_part():
    """Add new spare part"""
    if request.method == 'POST':
        try:
            # Create new spare part
            part = SparePart(
                name=request.form['name'],
                part_number=request.form['part_number'],
                description=request.form.get('description', ''),
                category=request.form['category'],
                brand=request.form.get('brand', ''),
                price=float(request.form['price']),
                quantity=int(request.form['quantity']),
                min_quantity=int(request.form.get('min_quantity', 5)),
                location=request.form.get('location', ''),
                supplier=request.form.get('supplier', '')
            )
            
            db.session.add(part)
            db.session.commit()
            
            # Create initial stock movement
            if part.quantity > 0:
                movement = StockMovement(
                    spare_part_id=part.id,
                    movement_type='initial_stock',
                    quantity=part.quantity,
                    notes=f'Initial stock entry for {part.name}',
                    user_id=current_user.id
                )
                db.session.add(movement)
                db.session.commit()
            
            flash(f'Spare part "{part.name}" added successfully!', 'success')
            return redirect(url_for('inventory_enhanced.part_detail', part_id=part.id))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Error adding spare part: {str(e)}', 'error')
    
    # Get existing categories and brands for form
    categories = db.session.query(SparePart.category).distinct().filter(SparePart.category.isnot(None)).all()
    categories = [cat[0] for cat in categories if cat[0]]
    
    brands = db.session.query(SparePart.brand).distinct().filter(SparePart.brand.isnot(None)).all()
    brands = [brand[0] for brand in brands if brand[0]]
    
    return render_template('inventory/add_part.html', categories=categories, brands=brands)

@inventory_enhanced_bp.route('/edit/<int:part_id>', methods=['GET', 'POST'])
@login_required
@admin_required
def edit_part(part_id):
    """Edit existing spare part"""
    part = SparePart.query.get_or_404(part_id)
    
    if request.method == 'POST':
        try:
            # Update part details
            part.name = request.form['name']
            part.part_number = request.form['part_number']
            part.description = request.form.get('description', '')
            part.category = request.form['category']
            part.brand = request.form.get('brand', '')
            part.price = float(request.form['price'])
            part.min_quantity = int(request.form.get('min_quantity', 5))
            part.location = request.form.get('location', '')
            part.supplier = request.form.get('supplier', '')
            
            db.session.commit()
            flash(f'Spare part "{part.name}" updated successfully!', 'success')
            return redirect(url_for('inventory_enhanced.part_detail', part_id=part.id))
            
        except Exception as e:
            db.session.rollback()
            flash(f'Error updating spare part: {str(e)}', 'error')
    
    # Get existing categories and brands for form
    categories = db.session.query(SparePart.category).distinct().filter(SparePart.category.isnot(None)).all()
    categories = [cat[0] for cat in categories if cat[0]]
    
    brands = db.session.query(SparePart.brand).distinct().filter(SparePart.brand.isnot(None)).all()
    brands = [brand[0] for brand in brands if brand[0]]
    
    return render_template('inventory/edit_part.html', part=part, categories=categories, brands=brands)

@inventory_enhanced_bp.route('/stock/<int:part_id>', methods=['POST'])
@login_required
@admin_required
def update_stock(part_id):
    """Update stock quantity"""
    part = SparePart.query.get_or_404(part_id)
    
    try:
        movement_type = request.form['movement_type']
        quantity = int(request.form['quantity'])
        notes = request.form.get('notes', '')
        
        old_quantity = part.quantity
        
        if movement_type == 'add':
            part.quantity += quantity
        elif movement_type == 'remove':
            if part.quantity < quantity:
                flash('Cannot remove more items than available in stock!', 'error')
                return redirect(url_for('inventory_enhanced.part_detail', part_id=part_id))
            part.quantity -= quantity
        elif movement_type == 'set':
            part.quantity = quantity
        
        # Create stock movement record
        movement = StockMovement(
            spare_part_id=part_id,
            movement_type=movement_type,
            quantity=quantity,
            old_quantity=old_quantity,
            new_quantity=part.quantity,
            notes=notes,
            user_id=current_user.id
        )
        
        db.session.add(movement)
        db.session.commit()
        
        flash(f'Stock updated successfully! New quantity: {part.quantity}', 'success')
        
    except Exception as e:
        db.session.rollback()
        flash(f'Error updating stock: {str(e)}', 'error')
    
    return redirect(url_for('inventory_enhanced.part_detail', part_id=part_id))

@inventory_enhanced_bp.route('/reports')
@login_required
def reports():
    """Inventory reports and analytics"""
    
    # Inventory value by category
    category_values = db.session.query(
        SparePart.category,
        func.sum(SparePart.price * SparePart.quantity).label('total_value'),
        func.count(SparePart.id).label('part_count')
    ).group_by(SparePart.category).all()
    
    # Top selling parts (based on repair usage)
    top_selling = db.session.query(
        SparePart,
        func.sum(RepairPartUsage.quantity).label('total_used')
    ).join(RepairPartUsage).group_by(SparePart.id).order_by(desc('total_used')).limit(10).all()
    
    # Stock movement trends (last 30 days)
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    movement_trends = db.session.query(
        func.date(StockMovement.created_at).label('date'),
        func.count(StockMovement.id).label('movement_count')
    ).filter(StockMovement.created_at >= thirty_days_ago).group_by(
        func.date(StockMovement.created_at)
    ).order_by('date').all()
    
    return render_template(
        'inventory/reports.html',
        category_values=category_values,
        top_selling=top_selling,
        movement_trends=movement_trends
    )
